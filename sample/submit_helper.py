#!/usr/bin/env python3
"""
计算物理课程提交助手
学生使用的简化提交函数

使用方法：
1. 将此文件放在你的Python程序同一目录
2. 在程序中导入: from submit_helper import submit_answer
3. 调用提交函数即可

示例：
    submit_answer('2022062030', 1, note='我的计算结果')
"""

import requests
import json
import matplotlib.pyplot as plt
from io import BytesIO
import numpy as np

# 服务器配置
# SERVER_URL = "http://**************:30200"  # 生产环境
SERVER_URL = "http://localhost:30200"     # 本地测试

def submit_answer(student_no, question_id, x_data=None, y_data=None, result_value=None, note=""):
    """
    提交答案到服务器
    
    参数:
        student_no (str): 学号，如 '2022062030'
        question_id (int): 题目ID
        x_data (list): X轴数据（可选）
        y_data (list): Y轴数据（可选）
        result_value (float): 计算结果数值（可选）
        note (str): 备注说明
    
    返回:
        dict: 提交结果 {'success': bool, 'message': str}
    """
    
    try:
        # 准备提交数据
        data = {
            'student_no': student_no,
            'question_id': question_id,
            'note': note
        }
        
        files = {}
        
        # 如果有数据，生成图像
        if x_data is not None and y_data is not None:
            # 创建图像
            plt.figure(figsize=(10, 6))
            plt.plot(x_data, y_data, 'b-', linewidth=2, marker='o', markersize=4)
            plt.title(f'学号: {student_no} - {note}', fontsize=14)
            plt.xlabel('X')
            plt.ylabel('Y')
            plt.grid(True, alpha=0.3)
            
            # 保存图像到内存
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            plt.close()
            
            # 添加图像文件
            files['image'] = ('result.png', buffer.getvalue(), 'image/png')
            
            # 添加数值数据
            data['numeric'] = json.dumps(y_data)
        
        # 如果只有结果数值
        elif result_value is not None:
            data['numeric'] = json.dumps([result_value])
        
        # 发送请求
        response = requests.post(f"{SERVER_URL}/api/submissions", 
                               files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ 提交成功! 学号: {student_no}, 题目: {question_id}")
            if note:
                print(f"   备注: {note}")
            return {'success': True, 'message': '提交成功'}
        else:
            error_msg = f"提交失败: {response.status_code}"
            try:
                error_detail = response.json().get('detail', '未知错误')
                error_msg += f" - {error_detail}"
            except:
                pass
            print(f"❌ {error_msg}")
            return {'success': False, 'message': error_msg}
            
    except requests.exceptions.Timeout:
        error_msg = "提交超时，请检查网络连接"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}
    except requests.exceptions.ConnectionError:
        error_msg = "无法连接到服务器，请检查网络或服务器状态"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}
    except Exception as e:
        error_msg = f"提交出错: {str(e)}"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}

def quick_submit(student_no, question_id, note=""):
    """
    快速提交当前matplotlib图形
    
    参数:
        student_no (str): 学号
        question_id (int): 题目ID  
        note (str): 备注说明
    """
    try:
        # 保存当前图形
        buffer = BytesIO()
        plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        
        # 提交数据
        data = {
            'student_no': student_no,
            'question_id': question_id,
            'note': note
        }
        
        files = {
            'image': ('result.png', buffer.getvalue(), 'image/png')
        }
        
        response = requests.post(f"{SERVER_URL}/api/submissions", 
                               files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            print(f"✅ 快速提交成功! 学号: {student_no}")
            return {'success': True, 'message': '提交成功'}
        else:
            error_msg = f"提交失败: {response.status_code}"
            print(f"❌ {error_msg}")
            return {'success': False, 'message': error_msg}
            
    except Exception as e:
        error_msg = f"快速提交出错: {str(e)}"
        print(f"❌ {error_msg}")
        return {'success': False, 'message': error_msg}

# 使用示例
if __name__ == "__main__":
    print("📚 计算物理提交助手")
    print("=" * 40)
    print("使用示例:")
    print("1. 提交计算结果和图形:")
    print("   x = [1, 2, 3, 4, 5]")
    print("   y = [1, 4, 9, 16, 25]")
    print("   submit_answer('2022062030', 1, x, y, note='二次函数')")
    print()
    print("2. 提交数值结果:")
    print("   submit_answer('2022062030', 1, result_value=3.14159, note='圆周率计算')")
    print()
    print("3. 快速提交当前图形:")
    print("   plt.plot([1,2,3], [1,4,9])")
    print("   quick_submit('2022062030', 1, '我的图形')")
    print()
    print(f"🌐 服务器: {SERVER_URL}")
