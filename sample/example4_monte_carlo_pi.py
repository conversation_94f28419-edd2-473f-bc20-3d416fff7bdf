#!/usr/bin/env python3
"""
计算物理示例4: 蒙特卡罗方法计算π
题目: 使用随机投点法估算圆周率

学生需要完成的部分:
1. 实现蒙特卡罗算法
2. 分析收敛性
3. 计算误差和置信区间
4. 可视化投点过程
"""

import numpy as np
import matplotlib.pyplot as plt
from submit_helper import submit_answer

# ==================== 学生信息 ====================
STUDENT_NO = "2022062033"  # 请修改为你的学号
QUESTION_ID = 4            # 题目ID (由教师提供)

# ==================== 题目要求 ====================
"""
蒙特卡罗计算π问题:
在单位正方形内随机投点，统计落在单位圆内的点数比例
π ≈ 4 × (圆内点数 / 总点数)

要求:
1. 实现随机投点算法
2. 分析不同样本数量的收敛性
3. 计算统计误差
4. 可视化投点分布和收敛过程
"""

def monte_carlo_pi(n_points):
    """
    蒙特卡罗方法计算π (学生需要完成)
    
    参数:
        n_points: 投点数量
    
    返回:
        pi_estimate: π的估计值
        points_in_circle: 圆内点数
        x_coords, y_coords: 所有点的坐标
        inside_circle: 每个点是否在圆内的布尔数组
    """
    print(f"🎯 投掷 {n_points:,} 个点...")
    
    # ========== 学生需要完成的算法 ==========
    # 生成随机点坐标 (0,1) 范围内
    x_coords = np.random.uniform(0, 1, n_points)
    y_coords = np.random.uniform(0, 1, n_points)
    
    # 计算每个点到原点的距离平方
    distances_squared = x_coords**2 + y_coords**2
    
    # 判断哪些点在单位圆内 (距离 <= 1)
    inside_circle = distances_squared <= 1
    
    # 统计圆内点数
    points_in_circle = np.sum(inside_circle)
    
    # 估算π值
    pi_estimate = 4 * points_in_circle / n_points
    
    return pi_estimate, points_in_circle, x_coords, y_coords, inside_circle

def analyze_convergence():
    """
    分析收敛性 (学生需要完成)
    """
    print("📈 分析收敛性...")
    
    # ========== 学生需要设置的参数 ==========
    sample_sizes = [100, 500, 1000, 5000, 10000, 50000, 100000]
    n_trials = 10  # 每个样本量重复次数
    
    # ========== 学生需要完成的分析 ==========
    convergence_data = []
    
    for n in sample_sizes:
        pi_estimates = []
        
        # 多次试验求平均
        for trial in range(n_trials):
            pi_est, _, _, _, _ = monte_carlo_pi(n)
            pi_estimates.append(pi_est)
        
        mean_pi = np.mean(pi_estimates)
        std_pi = np.std(pi_estimates)
        error = abs(mean_pi - np.pi)
        
        convergence_data.append({
            'n_points': n,
            'mean_pi': mean_pi,
            'std_pi': std_pi,
            'error': error,
            'estimates': pi_estimates
        })
        
        print(f"   N={n:6d}: π≈{mean_pi:.4f} ± {std_pi:.4f}, 误差={error:.4f}")
    
    return convergence_data

def visualize_results():
    """
    可视化结果 (学生需要完成)
    """
    print("🎨 生成可视化图形...")
    
    # 分析收敛性
    convergence_data = analyze_convergence()
    
    # 选择一个中等样本量进行详细可视化
    n_visual = 10000
    pi_est, points_in, x_coords, y_coords, inside = monte_carlo_pi(n_visual)
    
    # ========== 学生需要完成的绘图 ==========
    plt.figure(figsize=(16, 12))
    
    # 子图1: 投点分布可视化
    plt.subplot(2, 3, 1)
    # 只显示前1000个点以免过于密集
    n_show = min(1000, len(x_coords))
    inside_show = inside[:n_show]
    
    plt.scatter(x_coords[:n_show][inside_show], y_coords[:n_show][inside_show], 
               c='red', s=1, alpha=0.6, label=f'圆内 ({np.sum(inside_show)})')
    plt.scatter(x_coords[:n_show][~inside_show], y_coords[:n_show][~inside_show], 
               c='blue', s=1, alpha=0.6, label=f'圆外 ({np.sum(~inside_show)})')
    
    # 绘制单位圆
    theta = np.linspace(0, np.pi/2, 100)
    plt.plot(np.cos(theta), np.sin(theta), 'k-', linewidth=2)
    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.xlabel('x')
    plt.ylabel('y')
    plt.title(f'蒙特卡罗投点 - 学号: {STUDENT_NO}')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.gca().set_aspect('equal')
    
    # 子图2: 收敛过程
    plt.subplot(2, 3, 2)
    sample_sizes = [data['n_points'] for data in convergence_data]
    mean_pis = [data['mean_pi'] for data in convergence_data]
    errors = [data['error'] for data in convergence_data]
    
    plt.semilogx(sample_sizes, mean_pis, 'bo-', linewidth=2, markersize=6)
    plt.axhline(y=np.pi, color='r', linestyle='--', linewidth=2, label='真实值 π')
    plt.xlabel('样本数量 N')
    plt.ylabel('π 估计值')
    plt.title('收敛性分析')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图3: 误差分析
    plt.subplot(2, 3, 3)
    plt.loglog(sample_sizes, errors, 'ro-', linewidth=2, markersize=6, label='实际误差')
    # 理论误差 ∝ 1/√N
    theoretical_error = 0.1 / np.sqrt(np.array(sample_sizes))
    plt.loglog(sample_sizes, theoretical_error, 'g--', linewidth=2, label='理论误差 ∝ 1/√N')
    plt.xlabel('样本数量 N')
    plt.ylabel('绝对误差')
    plt.title('误差随样本量变化')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图4: 标准差分析
    plt.subplot(2, 3, 4)
    std_pis = [data['std_pi'] for data in convergence_data]
    plt.loglog(sample_sizes, std_pis, 'mo-', linewidth=2, markersize=6)
    plt.xlabel('样本数量 N')
    plt.ylabel('标准差')
    plt.title('估计值标准差')
    plt.grid(True, alpha=0.3)
    
    # 子图5: 分布直方图
    plt.subplot(2, 3, 5)
    # 选择一个样本量的所有估计值
    large_sample_data = convergence_data[-2]  # 倒数第二个样本量
    plt.hist(large_sample_data['estimates'], bins=15, alpha=0.7, density=True, 
             color='skyblue', edgecolor='black')
    plt.axvline(x=np.pi, color='r', linestyle='--', linewidth=2, label='真实值 π')
    plt.axvline(x=large_sample_data['mean_pi'], color='g', linestyle='-', 
                linewidth=2, label='平均估计值')
    plt.xlabel('π 估计值')
    plt.ylabel('概率密度')
    plt.title(f'估计值分布 (N={large_sample_data["n_points"]})')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图6: 累积平均
    plt.subplot(2, 3, 6)
    # 计算累积平均
    n_cumulative = 50000
    pi_est, _, x_cum, y_cum, inside_cum = monte_carlo_pi(n_cumulative)
    
    cumulative_pi = []
    for i in range(100, n_cumulative, 500):
        points_in_cum = np.sum(inside_cum[:i])
        pi_cum = 4 * points_in_cum / i
        cumulative_pi.append(pi_cum)
    
    x_axis = range(100, n_cumulative, 500)
    plt.plot(x_axis, cumulative_pi, 'b-', linewidth=2)
    plt.axhline(y=np.pi, color='r', linestyle='--', linewidth=2, label='真实值 π')
    plt.xlabel('累积样本数')
    plt.ylabel('累积平均π值')
    plt.title('累积平均收敛')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 返回主要结果
    final_result = convergence_data[-1]  # 最大样本量的结果
    return sample_sizes, mean_pis, {
        'final_estimate': final_result['mean_pi'],
        'final_error': final_result['error'],
        'final_std': final_result['std_pi'],
        'sample_size': final_result['n_points'],
        'relative_error': final_result['error'] / np.pi * 100,
        'confidence_interval': [
            final_result['mean_pi'] - 1.96 * final_result['std_pi'],
            final_result['mean_pi'] + 1.96 * final_result['std_pi']
        ]
    }

def main():
    """主函数"""
    print("📚 计算物理实验 - 蒙特卡罗方法计算π")
    print("=" * 50)
    
    # 学生解题
    x_data, y_data, results = visualize_results()
    
    # 显示最终结果
    print(f"\n📊 蒙特卡罗计算结果:")
    print(f"   样本数量: {results['sample_size']:,}")
    print(f"   π估计值: {results['final_estimate']:.6f}")
    print(f"   真实值: {np.pi:.6f}")
    print(f"   绝对误差: {results['final_error']:.6f}")
    print(f"   相对误差: {results['relative_error']:.3f}%")
    print(f"   标准差: {results['final_std']:.6f}")
    print(f"   95%置信区间: [{results['confidence_interval'][0]:.4f}, {results['confidence_interval'][1]:.4f}]")
    
    # 提交答案
    print(f"\n📤 提交答案到服务器...")
    result = submit_answer(
        student_no=STUDENT_NO,
        question_id=QUESTION_ID,
        x_data=x_data,
        y_data=y_data,
        result_value=results['final_estimate'],
        note=f"蒙特卡罗π计算 - N={results['sample_size']:,}, π≈{results['final_estimate']:.4f}, 误差={results['relative_error']:.2f}%"
    )
    
    if result['success']:
        print("🎉 实验完成！请在课堂展示页面查看结果")
        print("🌐 展示地址: http://43.155.146.157:30200/static/classroom_display.html")
    else:
        print(f"❌ 提交失败: {result['message']}")
        print("💡 请检查网络连接和服务器状态")

if __name__ == "__main__":
    main()
