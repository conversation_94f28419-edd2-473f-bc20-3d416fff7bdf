#!/usr/bin/env python3
"""
计算物理示例1: 简谐振动
题目: 计算并绘制简谐振动的位移-时间图像

学生需要完成的部分:
1. 设置振动参数
2. 计算位移随时间的变化
3. 绘制图像
4. 提交结果
"""

import numpy as np
import matplotlib.pyplot as plt
from submit_helper import submit_answer

# ==================== 学生信息 ====================
STUDENT_NO = "2022062030"  # 请修改为你的学号
QUESTION_ID = 2            # 题目ID (由教师提供)

# ==================== 题目要求 ====================
"""
简谐振动问题:
一个质量为m的物体在弹簧上做简谐振动，振动方程为:
x(t) = A * cos(ωt + φ)

要求:
1. 设置合适的振动参数 A, ω, φ
2. 计算0到4π时间范围内的位移
3. 绘制位移-时间图像
4. 提交计算结果
"""

def solve_harmonic_oscillator():
    """
    学生解题部分 - 简谐振动计算
    """
    print("🔬 开始计算简谐振动...")
    
    # ========== 学生需要设置的参数 ==========
    A = 2.0        # 振幅 (m)
    omega = 1.5    # 角频率 (rad/s)  
    phi = np.pi/4  # 初相位 (rad)
    
    # ========== 学生需要完成的计算 ==========
    # 时间范围
    t = np.linspace(0, 4*np.pi, 200)
    
    # 计算位移 (学生完成)
    x = A * np.cos(omega * t + phi)
    
    # 计算速度 (可选)
    v = -A * omega * np.sin(omega * t + phi)
    
    # ========== 学生需要完成的绘图 ==========
    plt.figure(figsize=(12, 8))
    
    # 位移图
    plt.subplot(2, 1, 1)
    plt.plot(t, x, 'b-', linewidth=2, label=f'位移 x(t)')
    plt.xlabel('时间 t (s)')
    plt.ylabel('位移 x (m)')
    plt.title(f'简谐振动 - 学号: {STUDENT_NO}')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 速度图
    plt.subplot(2, 1, 2)
    plt.plot(t, v, 'r-', linewidth=2, label=f'速度 v(t)')
    plt.xlabel('时间 t (s)')
    plt.ylabel('速度 v (m/s)')
    plt.title('简谐振动速度图')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    
    # 显示图形
    plt.show()
    
    # 返回计算结果
    return t.tolist(), x.tolist(), {
        'amplitude': A,
        'frequency': omega,
        'phase': phi,
        'period': 2*np.pi/omega,
        'max_displacement': np.max(x),
        'min_displacement': np.min(x)
    }

def main():
    """主函数"""
    print("📚 计算物理实验 - 简谐振动")
    print("=" * 50)
    
    # 学生解题
    t_data, x_data, results = solve_harmonic_oscillator()
    
    # 显示计算结果
    print(f"\n📊 计算结果:")
    print(f"   振幅: {results['amplitude']:.2f} m")
    print(f"   角频率: {results['frequency']:.2f} rad/s")
    print(f"   周期: {results['period']:.2f} s")
    print(f"   最大位移: {results['max_displacement']:.2f} m")
    print(f"   最小位移: {results['min_displacement']:.2f} m")
    
    # 提交答案
    print(f"\n📤 提交答案到服务器...")
    result = submit_answer(
        student_no=STUDENT_NO,
        question_id=QUESTION_ID,
        x_data=t_data,
        y_data=x_data,
        note=f"简谐振动实验 - A={results['amplitude']}, ω={results['frequency']}, φ={results['phase']:.2f}"
    )
    
    if result['success']:
        print("🎉 实验完成！请在课堂展示页面查看结果")
        print("🌐 展示地址: http://43.155.146.157:30200/static/classroom_display.html")
    else:
        print(f"❌ 提交失败: {result['message']}")
        print("💡 请检查网络连接和服务器状态")

if __name__ == "__main__":
    main()
