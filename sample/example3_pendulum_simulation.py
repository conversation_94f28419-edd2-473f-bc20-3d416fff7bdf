#!/usr/bin/env python3
"""
计算物理示例3: 单摆运动模拟
题目: 数值求解单摆运动方程并分析周期

学生需要完成的部分:
1. 设置单摆参数
2. 数值求解微分方程
3. 分析周期与振幅的关系
4. 比较线性和非线性结果
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.integrate import odeint
from submit_helper import submit_answer

# ==================== 学生信息 ====================
STUDENT_NO = "2022062032"  # 请修改为你的学号
QUESTION_ID = 3            # 题目ID (由教师提供)

# ==================== 题目要求 ====================
"""
单摆运动问题:
单摆的运动方程为: d²θ/dt² + (g/L)sin(θ) = 0

要求:
1. 设置单摆的长度L和初始条件
2. 数值求解非线性微分方程
3. 计算不同振幅下的周期
4. 与小角度近似结果比较
5. 绘制相空间轨迹
"""

def pendulum_equation(state, t, g, L):
    """
    单摆运动方程 (学生需要理解)
    state = [θ, dθ/dt]
    """
    theta, theta_dot = state
    
    # ========== 学生需要完成的微分方程 ==========
    # 非线性单摆方程: d²θ/dt² = -(g/L)sin(θ)
    theta_ddot = -(g/L) * np.sin(theta)
    
    return [theta_dot, theta_ddot]

def solve_pendulum():
    """
    学生解题部分 - 单摆运动模拟
    """
    print("🕰️ 开始模拟单摆运动...")
    
    # ========== 学生需要设置的参数 ==========
    g = 9.81    # 重力加速度 (m/s²)
    L = 1.0     # 摆长 (m)
    
    # 不同的初始角度
    theta0_list = [np.pi/6, np.pi/4, np.pi/3, np.pi/2]  # 30°, 45°, 60°, 90°
    
    # 时间范围
    t = np.linspace(0, 4*np.pi/np.sqrt(g/L), 1000)
    
    # ========== 学生需要完成的计算 ==========
    results = {}
    
    plt.figure(figsize=(15, 12))
    
    # 子图1: 不同振幅的角度-时间图
    plt.subplot(2, 2, 1)
    
    for i, theta0 in enumerate(theta0_list):
        # 初始条件 [角度, 角速度]
        initial_state = [theta0, 0]
        
        # 数值求解微分方程 (学生完成)
        solution = odeint(pendulum_equation, initial_state, t, args=(g, L))
        theta = solution[:, 0]
        theta_dot = solution[:, 1]
        
        # 绘制角度-时间图
        plt.plot(t, theta, linewidth=2, label=f'θ₀ = {theta0*180/np.pi:.0f}°')
        
        # 计算周期 (找到零点)
        zero_crossings = np.where(np.diff(np.sign(theta)))[0]
        if len(zero_crossings) >= 2:
            period = 2 * (t[zero_crossings[1]] - t[zero_crossings[0]])
            results[theta0] = {
                'period': period,
                'theta': theta,
                'theta_dot': theta_dot,
                'energy': 0.5 * L**2 * theta_dot**2 + g * L * (1 - np.cos(theta))
            }
    
    plt.xlabel('时间 t (s)')
    plt.ylabel('角度 θ (rad)')
    plt.title(f'单摆运动 - 学号: {STUDENT_NO}')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图2: 相空间轨迹
    plt.subplot(2, 2, 2)
    for theta0 in theta0_list:
        if theta0 in results:
            plt.plot(results[theta0]['theta'], results[theta0]['theta_dot'], 
                    linewidth=2, label=f'θ₀ = {theta0*180/np.pi:.0f}°')
    
    plt.xlabel('角度 θ (rad)')
    plt.ylabel('角速度 dθ/dt (rad/s)')
    plt.title('相空间轨迹')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图3: 周期与振幅关系
    plt.subplot(2, 2, 3)
    amplitudes = []
    periods = []
    linear_periods = []
    
    for theta0 in theta0_list:
        if theta0 in results:
            amplitudes.append(theta0 * 180/np.pi)
            periods.append(results[theta0]['period'])
            # 线性近似周期
            linear_periods.append(2 * np.pi * np.sqrt(L/g))
    
    plt.plot(amplitudes, periods, 'bo-', linewidth=2, markersize=8, label='非线性解')
    plt.plot(amplitudes, linear_periods, 'r--', linewidth=2, label='线性近似')
    plt.xlabel('初始角度 (度)')
    plt.ylabel('周期 T (s)')
    plt.title('周期与振幅关系')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图4: 能量守恒检验
    plt.subplot(2, 2, 4)
    for theta0 in theta0_list:
        if theta0 in results:
            energy = results[theta0]['energy']
            plt.plot(t, energy, linewidth=2, label=f'θ₀ = {theta0*180/np.pi:.0f}°')
    
    plt.xlabel('时间 t (s)')
    plt.ylabel('总能量 E (J)')
    plt.title('能量守恒检验')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 返回主要结果
    main_theta0 = np.pi/4  # 45度作为主要结果
    if main_theta0 in results:
        return t.tolist(), results[main_theta0]['theta'].tolist(), {
            'length': L,
            'gravity': g,
            'initial_angle': main_theta0,
            'period_nonlinear': results[main_theta0]['period'],
            'period_linear': 2 * np.pi * np.sqrt(L/g),
            'period_ratio': results[main_theta0]['period'] / (2 * np.pi * np.sqrt(L/g)),
            'max_energy': np.max(results[main_theta0]['energy']),
            'energy_conservation': np.std(results[main_theta0]['energy']) < 0.01
        }
    
    return t.tolist(), [], {}

def main():
    """主函数"""
    print("📚 计算物理实验 - 单摆运动模拟")
    print("=" * 50)
    
    # 学生解题
    t_data, theta_data, results = solve_pendulum()
    
    if results:
        # 显示分析结果
        print(f"\n📊 单摆分析结果:")
        print(f"   摆长: {results['length']:.2f} m")
        print(f"   初始角度: {results['initial_angle']*180/np.pi:.1f}°")
        print(f"   非线性周期: {results['period_nonlinear']:.3f} s")
        print(f"   线性近似周期: {results['period_linear']:.3f} s")
        print(f"   周期比值: {results['period_ratio']:.3f}")
        print(f"   能量守恒: {'✓' if results['energy_conservation'] else '✗'}")
        
        # 提交答案
        print(f"\n📤 提交答案到服务器...")
        result = submit_answer(
            student_no=STUDENT_NO,
            question_id=QUESTION_ID,
            x_data=t_data,
            y_data=theta_data,
            note=f"单摆模拟 - L={results['length']:.1f}m, 周期比={results['period_ratio']:.3f}"
        )
        
        if result['success']:
            print("🎉 实验完成！请在课堂展示页面查看结果")
            print("🌐 展示地址: http://43.155.146.157:30200/static/classroom_display.html")
        else:
            print(f"❌ 提交失败: {result['message']}")
            print("💡 请检查网络连接和服务器状态")
    else:
        print("❌ 计算出现问题，请检查代码")

if __name__ == "__main__":
    main()
