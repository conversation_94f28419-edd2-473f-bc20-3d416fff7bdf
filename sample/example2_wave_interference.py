#!/usr/bin/env python3
"""
计算物理示例2: 波的干涉
题目: 计算并可视化两个波源的干涉图样

学生需要完成的部分:
1. 设置两个波源的参数
2. 计算干涉波形
3. 绘制干涉图样
4. 分析干涉特征
"""

import numpy as np
import matplotlib.pyplot as plt
from submit_helper import submit_answer

# ==================== 学生信息 ====================
STUDENT_NO = "2022062031"  # 请修改为你的学号
QUESTION_ID = 2            # 题目ID (由教师提供)

# ==================== 题目要求 ====================
"""
波的干涉问题:
两个相干波源S1和S2，波动方程为:
y1(x,t) = A1 * sin(k*x - ω*t + φ1)
y2(x,t) = A2 * sin(k*x - ω*t + φ2)

要求:
1. 设置两个波源的振幅、频率、相位
2. 计算合成波 y = y1 + y2
3. 绘制干涉图样
4. 分析干涉的建设性和破坏性位置
"""

def solve_wave_interference():
    """
    学生解题部分 - 波的干涉计算
    """
    print("🌊 开始计算波的干涉...")
    
    # ========== 学生需要设置的参数 ==========
    A1 = 1.0       # 波源1振幅
    A2 = 0.8       # 波源2振幅
    omega = 2.0    # 角频率
    k = 1.0        # 波数
    phi1 = 0       # 波源1初相位
    phi2 = np.pi/3 # 波源2初相位
    
    # ========== 学生需要完成的计算 ==========
    # 空间和时间网格
    x = np.linspace(0, 4*np.pi, 300)
    t = 0  # 固定时刻的快照
    
    # 计算两个波 (学生完成)
    y1 = A1 * np.sin(k * x - omega * t + phi1)
    y2 = A2 * np.sin(k * x - omega * t + phi2)
    
    # 计算合成波 (学生完成)
    y_total = y1 + y2
    
    # 计算包络线 (干涉强度)
    envelope = np.sqrt((A1 * np.cos(phi1) + A2 * np.cos(phi2))**2 + 
                      (A1 * np.sin(phi1) + A2 * np.sin(phi2))**2)
    
    # ========== 学生需要完成的绘图 ==========
    plt.figure(figsize=(14, 10))
    
    # 子图1: 单独的波
    plt.subplot(3, 1, 1)
    plt.plot(x, y1, 'b-', linewidth=2, label=f'波源1: A₁={A1}, φ₁={phi1:.2f}')
    plt.plot(x, y2, 'r-', linewidth=2, label=f'波源2: A₂={A2}, φ₂={phi2:.2f}')
    plt.xlabel('位置 x')
    plt.ylabel('振幅 y')
    plt.title(f'单独波形 - 学号: {STUDENT_NO}')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图2: 合成波
    plt.subplot(3, 1, 2)
    plt.plot(x, y_total, 'g-', linewidth=2, label='合成波 y₁ + y₂')
    plt.plot(x, envelope, 'k--', linewidth=1, alpha=0.7, label='包络线')
    plt.plot(x, -envelope, 'k--', linewidth=1, alpha=0.7)
    plt.xlabel('位置 x')
    plt.ylabel('振幅 y')
    plt.title('波的干涉结果')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 子图3: 强度分布
    plt.subplot(3, 1, 3)
    intensity = y_total**2
    plt.plot(x, intensity, 'm-', linewidth=2, label='干涉强度 I ∝ y²')
    plt.xlabel('位置 x')
    plt.ylabel('强度 I')
    plt.title('干涉强度分布')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 分析干涉特征
    max_intensity = np.max(intensity)
    min_intensity = np.min(intensity)
    contrast = (max_intensity - min_intensity) / (max_intensity + min_intensity)
    
    return x.tolist(), y_total.tolist(), {
        'amplitude1': A1,
        'amplitude2': A2,
        'phase_diff': phi2 - phi1,
        'max_intensity': max_intensity,
        'min_intensity': min_intensity,
        'contrast': contrast,
        'interference_type': 'constructive' if contrast > 0.5 else 'partial'
    }

def main():
    """主函数"""
    print("📚 计算物理实验 - 波的干涉")
    print("=" * 50)
    
    # 学生解题
    x_data, y_data, results = solve_wave_interference()
    
    # 显示分析结果
    print(f"\n📊 干涉分析结果:")
    print(f"   波源1振幅: {results['amplitude1']:.2f}")
    print(f"   波源2振幅: {results['amplitude2']:.2f}")
    print(f"   相位差: {results['phase_diff']:.2f} rad")
    print(f"   最大强度: {results['max_intensity']:.2f}")
    print(f"   最小强度: {results['min_intensity']:.2f}")
    print(f"   对比度: {results['contrast']:.2f}")
    print(f"   干涉类型: {results['interference_type']}")
    
    # 提交答案
    print(f"\n📤 提交答案到服务器...")
    result = submit_answer(
        student_no=STUDENT_NO,
        question_id=QUESTION_ID,
        x_data=x_data,
        y_data=y_data,
        note=f"波的干涉实验 - 相位差={results['phase_diff']:.2f}, 对比度={results['contrast']:.2f}"
    )
    
    if result['success']:
        print("🎉 实验完成！请在课堂展示页面查看结果")
        print("🌐 展示地址: http://43.155.146.157:30200/static/classroom_display.html")
    else:
        print(f"❌ 提交失败: {result['message']}")
        print("💡 请检查网络连接和服务器状态")

if __name__ == "__main__":
    main()
