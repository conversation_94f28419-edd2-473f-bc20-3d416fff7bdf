from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import UPLOAD_DIR, ensure_directories
from app.db.init_db import init_database
from app.routers import admin, api, public

# 确保目录存在
ensure_directories()

# 初始化数据库
init_database()

# 创建 FastAPI 应用
app = FastAPI(
    title="学生作业提交系统",
    description="支持图片和数字数组提交的作业管理系统",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(admin.router)
app.include_router(api.router)
app.include_router(public.router)

# 静态文件服务
app.mount("/uploads", StaticFiles(directory=UPLOAD_DIR), name="uploads")
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
def read_root():
    """根路径 - 重定向到静态页面"""
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/static/index.html")

@app.get("/health")
def health_check():
    """健康检查"""
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    import os
    port = int(os.getenv("PORT", 30200))
    uvicorn.run(app, host="0.0.0.0", port=port)
