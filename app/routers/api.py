from typing import Optional
from fastapi import APIRouter, Depends, Form, File, UploadFile, HTTPException, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.schemas import SubmissionCreateResponse
from app.services.submissions import (
    get_active_class, get_class_by_code, validate_question_for_submission,
    parse_numeric_array, get_or_create_submission, update_submission_files,
    update_submission_numeric
)
from app.services.files import validate_image, save_image_with_thumbnail

router = APIRouter(prefix="/api", tags=["api"])

@router.post("/submissions", response_model=SubmissionCreateResponse)
async def create_submission(
    question_id: int = Form(...),
    student_no: str = Form(...),
    class_code: Optional[str] = Form(None),
    student_name: Optional[str] = Form(None),
    note: Optional[str] = Form(None),
    numeric: Optional[str] = Form(None),
    text_content: Optional[str] = Form(None),
    image: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db)
):
    """学生提交作业"""
    
    # 1. 确定班级
    if class_code:
        class_obj = get_class_by_code(db, class_code)
    else:
        class_obj = get_active_class(db)
    
    # 2. 验证题目
    question = validate_question_for_submission(db, question_id, class_obj.id)
    
    # 3. 解析数字数组
    numeric_data = parse_numeric_array(numeric)
    
    # 4. 验证图片（如果有）
    image_saved = False
    if image and image.filename:
        is_valid, error_msg = validate_image(image)
        if not is_valid:
            if "不支持的图片类型" in error_msg:
                raise HTTPException(status_code=415, detail=error_msg)
            elif "大小超过限制" in error_msg:
                raise HTTPException(status_code=413, detail=error_msg)
            else:
                raise HTTPException(status_code=400, detail=error_msg)
    
    # 5. 获取或创建提交记录
    submission = get_or_create_submission(
        db=db,
        question_id=question_id,
        student_no=student_no,
        class_id=class_obj.id,
        student_name=student_name,
        note=note
    )
    
    # 6. 处理图片上传
    if image and image.filename:
        try:
            image_path, thumb_path, file_size = save_image_with_thumbnail(
                image, class_obj.id, question_id, student_no
            )
            
            # 确定 MIME 类型
            mime_type = image.content_type
            if not mime_type and image.filename:
                filename_lower = image.filename.lower()
                if filename_lower.endswith(('.jpg', '.jpeg')):
                    mime_type = 'image/jpeg'
                elif filename_lower.endswith('.png'):
                    mime_type = 'image/png'
                else:
                    mime_type = 'image/jpeg'  # 默认

            update_submission_files(
                submission=submission,
                image_path=image_path,
                thumb_path=thumb_path,
                mime=mime_type,
                size=file_size
            )
            image_saved = True
            
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"图片保存失败: {str(e)}"
            )
    
    # 7. 更新数字数据
    update_submission_numeric(submission, numeric_data)

    # 8. 更新文本内容
    if text_content is not None:
        submission.text_content = text_content

    # 9. 提交到数据库
    try:
        db.commit()
        db.refresh(submission)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"数据保存失败: {str(e)}"
        )
    
    return SubmissionCreateResponse(
        submission_id=submission.id,
        image={"saved": image_saved},
        numeric_count=len(numeric_data)
    )
