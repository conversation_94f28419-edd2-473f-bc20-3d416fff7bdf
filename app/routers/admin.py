import csv
import io
import json
from datetime import timedelta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session

from app.core.security import get_current_admin, create_access_token, verify_password
from app.core.config import ACCESS_TOKEN_EXPIRE_MINUTES
from app.db.session import get_db
from app.models import AdminUser, Class, Question, Submission, Student
from app.schemas import (
    LoginRequest, LoginResponse, ClassCreate, ClassUpdate, ClassOut,
    QuestionCreate, QuestionUpdate, QuestionOut, SubmissionOut, SubmissionUpdateByAdmin,
    StudentCreate, StudentUpdate, StudentOut, StudentImport
)
from app.services.files import get_image_url

router = APIRouter(prefix="/admin", tags=["admin"])

@router.post("/login", response_model=LoginResponse)
def login(request: LoginRequest, db: Session = Depends(get_db)):
    """管理员登录"""
    admin = db.query(AdminUser).filter(AdminUser.username == request.username).first()
    if not admin or not verify_password(request.password, admin.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": admin.username}, expires_delta=access_token_expires
    )
    
    return LoginResponse(access_token=access_token)

@router.get("/me")
def get_current_user(current_admin: AdminUser = Depends(get_current_admin)):
    """获取当前管理员信息"""
    return {"username": current_admin.username, "id": current_admin.id}

@router.get("/classes", response_model=List[ClassOut])
def get_classes(
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取所有班级"""
    classes = db.query(Class).order_by(Class.created_at.desc()).all()
    return classes

@router.post("/classes", response_model=ClassOut)
def create_class(
    request: ClassCreate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """创建班级"""
    # 检查班级代码是否已存在
    if request.code:
        existing = db.query(Class).filter(Class.code == request.code).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"班级代码 '{request.code}' 已存在"
            )
    
    new_class = Class(name=request.name, code=request.code)
    db.add(new_class)
    db.commit()
    db.refresh(new_class)
    
    return new_class

@router.patch("/classes/{class_id}", response_model=ClassOut)
def update_class(
    class_id: int,
    request: ClassUpdate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """更新班级"""
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 检查班级代码冲突
    if request.code and request.code != class_obj.code:
        existing = db.query(Class).filter(Class.code == request.code).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"班级代码 '{request.code}' 已存在"
            )
    
    if request.name is not None:
        class_obj.name = request.name
    if request.code is not None:
        class_obj.code = request.code
    
    db.commit()
    db.refresh(class_obj)
    return class_obj

@router.patch("/classes/{class_id}/activate")
def activate_class(
    class_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """激活班级"""
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 将所有班级设为非激活
    db.query(Class).update({Class.is_active: False})
    
    # 激活指定班级
    class_obj.is_active = True
    db.commit()
    
    return {"message": f"班级 '{class_obj.name}' 已激活"}

@router.delete("/classes/{class_id}")
def delete_class(
    class_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """删除班级"""
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    # 检查是否有关联的学生
    student_count = db.query(Student).filter(Student.class_id == class_id).count()
    if student_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除班级，还有 {student_count} 个学生关联到此班级"
        )

    # 检查是否有关联的题目
    question_count = db.query(Question).filter(Question.class_id == class_id).count()
    if question_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除班级，还有 {question_count} 个题目关联到此班级"
        )

    db.delete(class_obj)
    db.commit()

    return {"message": f"班级 '{class_obj.name}' 删除成功"}

# 任务管理 (原题目管理)
@router.get("/tasks", response_model=List[QuestionOut])
def get_tasks(
    class_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取任务列表"""
    query = db.query(Question)
    if class_id:
        query = query.filter(Question.class_id == class_id)

    tasks = query.order_by(Question.order, Question.created_at).all()
    return tasks

@router.get("/classes/{class_id}/tasks", response_model=List[QuestionOut])
def get_class_tasks(
    class_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取班级的所有任务"""
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    tasks = db.query(Question).filter(
        Question.class_id == class_id
    ).order_by(Question.order, Question.created_at).all()

    return tasks

@router.post("/tasks", response_model=QuestionOut)
def create_task(
    request: QuestionCreate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """创建任务"""
    # 验证班级存在
    class_obj = db.query(Class).filter(Class.id == request.class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    new_task = Question(
        class_id=request.class_id,
        title=request.title,
        description=request.description,
        experiment_type=request.experiment_type or "general",
        submission_type=request.submission_type or "image_data",
        order=request.order or 0,
        is_open=request.is_open if request.is_open is not None else True,
        due_at=request.due_at
    )

    db.add(new_task)
    db.commit()
    db.refresh(new_task)

    return new_task

@router.patch("/tasks/{task_id}", response_model=QuestionOut)
def update_task(
    task_id: int,
    request: QuestionUpdate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """更新任务"""
    task = db.query(Question).filter(Question.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    if request.title is not None:
        task.title = request.title
    if request.description is not None:
        task.description = request.description
    if request.experiment_type is not None:
        task.experiment_type = request.experiment_type
    if request.submission_type is not None:
        task.submission_type = request.submission_type
    if request.order is not None:
        task.order = request.order
    if request.is_open is not None:
        task.is_open = request.is_open
    if request.due_at is not None:
        task.due_at = request.due_at

    db.commit()
    db.refresh(task)
    return task

@router.delete("/tasks/{task_id}")
def delete_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """删除任务"""
    task = db.query(Question).filter(Question.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 检查是否有关联的提交
    submission_count = db.query(Submission).filter(Submission.question_id == task_id).count()
    if submission_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除任务，还有 {submission_count} 个提交关联到此任务"
        )

    db.delete(task)
    db.commit()

    return {"message": f"任务 '{task.title}' 删除成功"}

# 题目管理API (新增)
@router.get("/questions")
async def get_questions(class_id: Optional[int] = None, db: Session = Depends(get_db)):
    """获取题目列表"""
    query = db.query(Question)
    if class_id:
        query = query.filter(Question.class_id == class_id)

    questions = query.all()

    # 添加班级名称和提交数量
    result = []
    for question in questions:
        class_name = question.class_.name if question.class_ else None
        submission_count = db.query(Submission).filter(Submission.question_id == question.id).count()

        result.append({
            "id": question.id,
            "title": question.title,
            "description": question.description,
            "question_type": getattr(question, 'question_type', 'submit_result'),
            "submission_type": question.submission_type,
            "is_open": question.is_open,
            "is_published": getattr(question, 'is_published', False),
            "published_classes": getattr(question, 'published_classes', None),
            "class_id": question.class_id,
            "class_name": class_name,
            "submission_count": submission_count,
            "created_at": question.created_at
        })

    return result

@router.post("/questions")
async def create_question(question: QuestionCreate, db: Session = Depends(get_db)):
    """创建题目"""
    # 验证班级是否存在
    class_obj = db.query(Class).filter(Class.id == question.class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    db_question = Question(
        class_id=question.class_id,
        title=question.title,
        description=question.description,
        submission_type=question.submission_type,
        is_open=question.is_open
    )

    # 如果模型支持新字段，则设置
    if hasattr(Question, 'question_type'):
        db_question.question_type = getattr(question, 'question_type', 'submit_result')

    db.add(db_question)
    db.commit()
    db.refresh(db_question)

    return {"message": "题目创建成功", "question_id": db_question.id}

@router.get("/questions/{question_id}")
async def get_question(question_id: int, db: Session = Depends(get_db)):
    """获取单个题目详情"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    return {
        "id": question.id,
        "title": question.title,
        "description": question.description,
        "question_type": getattr(question, 'question_type', 'submit_result'),
        "submission_type": question.submission_type,
        "is_open": question.is_open,
        "is_published": getattr(question, 'is_published', False),
        "published_classes": getattr(question, 'published_classes', None),
        "class_id": question.class_id,
        "created_at": question.created_at
    }

@router.patch("/questions/{question_id}")
async def update_question(question_id: int, update_data: dict, db: Session = Depends(get_db)):
    """更新题目"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    for key, value in update_data.items():
        if hasattr(question, key):
            setattr(question, key, value)

    db.commit()
    return {"message": "题目更新成功"}

@router.post("/questions/{question_id}/publish")
async def publish_question(question_id: int, publish_data: dict, db: Session = Depends(get_db)):
    """发布题目到指定班级"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    class_ids = publish_data.get("class_ids", [])
    if not class_ids:
        raise HTTPException(status_code=400, detail="请选择至少一个班级")

    # 验证班级是否存在
    for class_id in class_ids:
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail=f"班级ID {class_id} 不存在")

    # 更新发布状态
    if hasattr(question, 'is_published'):
        question.is_published = True
    if hasattr(question, 'published_classes'):
        question.published_classes = json.dumps(class_ids)

    db.commit()
    return {"message": "题目发布成功"}

@router.delete("/questions/{question_id}")
async def delete_question(question_id: int, db: Session = Depends(get_db)):
    """删除题目"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    # 检查是否有提交记录
    submission_count = db.query(Submission).filter(Submission.question_id == question_id).count()
    if submission_count > 0:
        raise HTTPException(status_code=400, detail=f"无法删除题目，存在 {submission_count} 个提交记录")

    db.delete(question)
    db.commit()
    return {"message": "题目删除成功"}

@router.get("/submissions", response_model=List[SubmissionOut])
def get_submissions(
    class_id: Optional[int] = None,
    question_id: Optional[int] = None,
    student_no: Optional[str] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取提交列表"""
    query = db.query(Submission)

    if class_id:
        query = query.filter(Submission.class_id == class_id)
    if question_id:
        query = query.filter(Submission.question_id == question_id)
    if student_no:
        query = query.filter(Submission.student_no.contains(student_no))

    submissions = query.order_by(Submission.updated_at.desc()).all()

    # 转换为响应格式
    result = []
    for sub in submissions:
        numeric_data = None
        if sub.numeric_json:
            try:
                numeric_data = json.loads(sub.numeric_json)
            except:
                pass

        result.append(SubmissionOut(
            id=sub.id,
            class_id=sub.class_id,
            question_id=sub.question_id,
            student_no=sub.student_no,
            student_name=sub.student_name,
            note=sub.note,
            numeric=numeric_data,
            image_url=get_image_url(sub.image_path),
            thumb_url=get_image_url(sub.thumb_path),
            mime=sub.mime,
            size=sub.size,
            score=sub.score,
            comment=sub.comment,
            visible=sub.visible,
            created_at=sub.created_at,
            updated_at=sub.updated_at
        ))

    return result

@router.get("/submissions/{submission_id}", response_model=SubmissionOut)
def get_submission(
    submission_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取单个提交详情"""
    submission = db.query(Submission).filter(Submission.id == submission_id).first()
    if not submission:
        raise HTTPException(status_code=404, detail="提交不存在")

    numeric_data = None
    if submission.numeric_json:
        try:
            numeric_data = json.loads(submission.numeric_json)
        except:
            pass

    return SubmissionOut(
        id=submission.id,
        class_id=submission.class_id,
        question_id=submission.question_id,
        student_no=submission.student_no,
        student_name=submission.student_name,
        note=submission.note,
        numeric=numeric_data,
        image_url=get_image_url(submission.image_path),
        thumb_url=get_image_url(submission.thumb_path),
        mime=submission.mime,
        size=submission.size,
        score=submission.score,
        comment=submission.comment,
        visible=submission.visible,
        created_at=submission.created_at,
        updated_at=submission.updated_at
    )

@router.patch("/submissions/{submission_id}", response_model=SubmissionOut)
def update_submission(
    submission_id: int,
    request: SubmissionUpdateByAdmin,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """更新提交（管理员）"""
    submission = db.query(Submission).filter(Submission.id == submission_id).first()
    if not submission:
        raise HTTPException(status_code=404, detail="提交不存在")

    if request.score is not None:
        submission.score = request.score
    if request.comment is not None:
        submission.comment = request.comment
    if request.visible is not None:
        submission.visible = request.visible

    db.commit()
    db.refresh(submission)

    # 返回更新后的数据
    numeric_data = None
    if submission.numeric_json:
        try:
            numeric_data = json.loads(submission.numeric_json)
        except:
            pass

    return SubmissionOut(
        id=submission.id,
        class_id=submission.class_id,
        question_id=submission.question_id,
        student_no=submission.student_no,
        student_name=submission.student_name,
        note=submission.note,
        numeric=numeric_data,
        image_url=get_image_url(submission.image_path),
        thumb_url=get_image_url(submission.thumb_path),
        mime=submission.mime,
        size=submission.size,
        score=submission.score,
        comment=submission.comment,
        visible=submission.visible,
        created_at=submission.created_at,
        updated_at=submission.updated_at
    )

@router.get("/export/csv")
def export_csv(
    class_id: Optional[int] = None,
    question_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """导出 CSV"""
    query = db.query(Submission)

    if class_id:
        query = query.filter(Submission.class_id == class_id)
    if question_id:
        query = query.filter(Submission.question_id == question_id)

    submissions = query.order_by(Submission.student_no, Submission.question_id).all()

    # 生成 CSV
    output = io.StringIO()
    writer = csv.writer(output)

    # 写入表头
    writer.writerow([
        "学号", "姓名", "题目ID", "得分", "是否可见", "提交时间",
        "数值数组", "图片路径", "备注"
    ])

    # 写入数据
    for sub in submissions:
        writer.writerow([
            sub.student_no,
            sub.student_name or "",
            sub.question_id,
            sub.score or "",
            "是" if sub.visible else "否",
            sub.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            sub.numeric_json or "",
            sub.image_path or "",
            sub.note or ""
        ])

    csv_content = output.getvalue()
    output.close()

    return Response(
        content=csv_content,
        media_type="text/csv",
        headers={"Content-Disposition": "attachment; filename=submissions.csv"}
    )

# 学生管理路由
@router.get("/students", response_model=List[StudentOut])
def get_students(
    class_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取学生列表"""
    query = db.query(Student)
    if class_id:
        query = query.filter(Student.class_id == class_id)

    students = query.all()

    # 添加班级名称和提交数量
    result = []
    for student in students:
        student_dict = {
            "id": student.id,
            "student_no": student.student_no,
            "name": student.name,
            "class_id": student.class_id,
            "email": student.email,
            "created_at": student.created_at,
            "class_name": student.class_.name if student.class_ else None,
            "submission_count": len(student.submissions)
        }
        result.append(student_dict)

    return result

@router.post("/students", response_model=StudentOut)
def create_student(
    student: StudentCreate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """创建学生"""
    # 检查班级是否存在
    class_obj = db.query(Class).filter(Class.id == student.class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    # 检查学号是否在班级内重复
    existing = db.query(Student).filter(
        Student.class_id == student.class_id,
        Student.student_no == student.student_no
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="该班级内学号已存在")

    db_student = Student(**student.dict())
    db.add(db_student)
    db.commit()
    db.refresh(db_student)

    return db_student

@router.post("/students/import")
def import_students(
    data: dict,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """批量导入学生"""
    class_id = data.get("class_id")
    students_data = data.get("students", [])

    if not class_id or not students_data:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    # 检查班级是否存在
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    success_count = 0
    errors = []

    for student_data in students_data:
        try:
            # 检查学号是否重复
            existing = db.query(Student).filter(
                Student.class_id == class_id,
                Student.student_no == student_data["student_no"]
            ).first()

            if existing:
                errors.append(f"学号 {student_data['student_no']} 已存在")
                continue

            db_student = Student(
                class_id=class_id,
                student_no=student_data["student_no"],
                name=student_data["name"],
                email=student_data.get("email")
            )
            db.add(db_student)
            success_count += 1

        except Exception as e:
            errors.append(f"学号 {student_data.get('student_no', '未知')}: {str(e)}")

    db.commit()

    return {
        "success_count": success_count,
        "errors": errors,
        "message": f"成功导入 {success_count} 个学生"
    }

@router.patch("/students/{student_id}", response_model=StudentOut)
def update_student(
    student_id: int,
    student: StudentUpdate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """更新学生信息"""
    db_student = db.query(Student).filter(Student.id == student_id).first()
    if not db_student:
        raise HTTPException(status_code=404, detail="学生不存在")

    update_data = student.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_student, field, value)

    db.commit()
    db.refresh(db_student)

    return db_student

@router.delete("/students/{student_id}")
def delete_student(
    student_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """删除学生"""
    db_student = db.query(Student).filter(Student.id == student_id).first()
    if not db_student:
        raise HTTPException(status_code=404, detail="学生不存在")

    db.delete(db_student)
    db.commit()

    return {"message": "学生删除成功"}
