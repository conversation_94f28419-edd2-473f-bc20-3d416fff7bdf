import json
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models import Submission, Question, Class, Student
from app.schemas import PublicSubmissionOut, QuestionOut
from app.services.submissions import mask_student_no
from app.services.files import get_image_url

router = APIRouter(prefix="/public", tags=["public"])

@router.get("/classes")
def get_public_classes(db: Session = Depends(get_db)):
    """获取公开的班级列表"""
    classes = db.query(Class).all()
    return [{"id": cls.id, "name": cls.name, "is_active": cls.is_active} for cls in classes]

@router.get("/questions")
def get_public_questions(db: Session = Depends(get_db)):
    """获取公开的题目列表"""
    questions = db.query(Question).all()
    return [{"id": q.id, "title": q.title, "is_open": q.is_open, "class_id": q.class_id} for q in questions]

@router.get("/submissions")
def get_public_submissions(
    class_id: Optional[int] = Query(None),
    question_id: Optional[int] = Query(None),
    visible: Optional[bool] = Query(None),
    db: Session = Depends(get_db)
):
    """获取公开的提交列表"""
    query = db.query(Submission)

    if class_id:
        query = query.join(Student).filter(Student.class_id == class_id)
    if question_id:
        query = query.filter(Submission.question_id == question_id)
    if visible is not None:
        query = query.filter(Submission.visible == visible)

    submissions = query.all()

    result = []
    for submission in submissions:
        student = db.query(Student).filter(Student.student_no == submission.student_no).first()
        question = db.query(Question).filter(Question.id == submission.question_id).first()

        result.append({
            "id": submission.id,
            "student_no": submission.student_no,
            "student_name": student.name if student else None,
            "question_id": submission.question_id,
            "question_title": question.title if question else None,
            "image_url": get_image_url(submission.image_path) if submission.image_path else None,
            "numeric": submission.numeric,
            "note": submission.note,
            "score": submission.score,
            "visible": submission.visible,
            "created_at": submission.created_at
        })

    return result

@router.get("/open-questions", response_model=List[QuestionOut])
def get_open_questions(db: Session = Depends(get_db)):
    """获取开放的实验列表"""
    # 获取激活班级的开放实验
    active_class = db.query(Class).filter(Class.is_active == True).first()
    if not active_class:
        return []

    questions = db.query(Question).filter(
        Question.class_id == active_class.id,
        Question.is_open == True
    ).order_by(Question.order, Question.created_at).all()

    return questions

@router.get("/showcase", response_model=List[PublicSubmissionOut])
def get_showcase(
    class_id: Optional[int] = Query(None),
    question_id: Optional[int] = Query(None),
    anonymous: bool = Query(True),
    sort_by: str = Query("time", description="排序方式: time(时间), score(分数), student_no(学号)"),
    sort_order: str = Query("desc", description="排序顺序: asc(升序), desc(降序)"),
    db: Session = Depends(get_db)
):
    """获取公开展示的提交"""
    query = db.query(Submission).filter(Submission.visible == True)

    if class_id:
        query = query.filter(Submission.class_id == class_id)
    if question_id:
        query = query.filter(Submission.question_id == question_id)

    # 排序逻辑
    if sort_by == "time":
        if sort_order == "asc":
            query = query.order_by(Submission.created_at.asc())
        else:
            query = query.order_by(Submission.created_at.desc())
    elif sort_by == "score":
        if sort_order == "asc":
            query = query.order_by(Submission.score.asc().nulls_last())
        else:
            query = query.order_by(Submission.score.desc().nulls_last())
    elif sort_by == "student_no":
        if sort_order == "asc":
            query = query.order_by(Submission.student_no.asc())
        else:
            query = query.order_by(Submission.student_no.desc())
    else:
        # 默认按时间降序
        query = query.order_by(Submission.created_at.desc())

    submissions = query.all()

    result = []
    for sub in submissions:
        # 解析数字数组
        numeric_data = None
        if sub.numeric_json:
            try:
                numeric_data = json.loads(sub.numeric_json)
            except:
                pass

        result.append(PublicSubmissionOut(
            submission_id=sub.id,
            student_no_masked=mask_student_no(sub.student_no, anonymous),
            image_url=get_image_url(sub.image_path),
            thumb_url=get_image_url(sub.thumb_path),
            note=sub.note,
            created_at=sub.created_at,
            score=sub.score,
            numeric=numeric_data
        ))

    return result

@router.get("/showcase/view", response_class=HTMLResponse)
def get_showcase_html(
    class_id: Optional[int] = Query(None),
    question_id: Optional[int] = Query(None),
    anonymous: bool = Query(True),
    sort_by: str = Query("time", description="排序方式: time(时间), score(分数), student_no(学号)"),
    sort_order: str = Query("desc", description="排序顺序: asc(升序), desc(降序)"),
    db: Session = Depends(get_db)
):
    """获取公开展示的 HTML 页面"""
    # 获取提交数据
    submissions = get_showcase(class_id, question_id, anonymous, sort_by, sort_order, db)

    # 生成 HTML
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>作业展示</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .header {{
                text-align: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                margin-bottom: 30px;
            }}
            .controls {{
                background: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .controls select, .controls button {{
                padding: 8px 12px;
                margin: 5px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }}
            .controls button {{
                background: #667eea;
                color: white;
                cursor: pointer;
            }}
            .controls button:hover {{
                background: #5a6fd8;
            }}
            .submissions-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                gap: 20px;
            }}
            .submission-card {{
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                overflow: hidden;
                transition: transform 0.2s;
            }}
            .submission-card:hover {{
                transform: translateY(-5px);
            }}
            .submission-image {{
                width: 100%;
                height: 200px;
                object-fit: cover;
                background: #f8f9fa;
            }}
            .submission-content {{
                padding: 20px;
            }}
            .student-info {{
                font-weight: bold;
                color: #333;
                margin-bottom: 10px;
            }}
            .score {{
                display: inline-block;
                background: #28a745;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                margin-bottom: 10px;
            }}
            .numeric-data {{
                background: #f8f9fa;
                padding: 10px;
                border-radius: 5px;
                font-family: monospace;
                margin: 10px 0;
            }}
            .note {{
                color: #666;
                font-style: italic;
                margin: 10px 0;
            }}
            .timestamp {{
                color: #999;
                font-size: 12px;
                text-align: right;
            }}
            .no-image {{
                height: 200px;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f8f9fa;
                color: #999;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎓 作业展示</h1>
            <p>共 {len(submissions)} 条可见提交</p>
        </div>

        <div class="controls">
            <label>排序方式:</label>
            <select id="sortBy" onchange="updateSort()">
                <option value="time" {"selected" if sort_by == "time" else ""}>时间</option>
                <option value="score" {"selected" if sort_by == "score" else ""}>分数</option>
                <option value="student_no" {"selected" if sort_by == "student_no" else ""}>学号</option>
            </select>

            <label>排序顺序:</label>
            <select id="sortOrder" onchange="updateSort()">
                <option value="desc" {"selected" if sort_order == "desc" else ""}>降序</option>
                <option value="asc" {"selected" if sort_order == "asc" else ""}>升序</option>
            </select>

            <label>匿名显示:</label>
            <select id="anonymous" onchange="updateSort()">
                <option value="true" {"selected" if anonymous else ""}>是</option>
                <option value="false" {"selected" if not anonymous else ""}>否</option>
            </select>

            <button onclick="location.reload()">刷新</button>
        </div>

        <div class="submissions-grid">
    """

    for sub in submissions:
        # 格式化时间
        time_str = sub.created_at.strftime("%Y-%m-%d %H:%M:%S")

        # 分数显示
        score_html = f'<span class="score">分数: {sub.score}</span>' if sub.score is not None else ''

        # 数字数据显示
        numeric_html = ""
        if sub.numeric:
            numeric_str = ", ".join([f"{x:.2f}" if isinstance(x, float) else str(x) for x in sub.numeric])
            numeric_html = f'<div class="numeric-data">数据: [{numeric_str}]</div>'

        # 图片显示
        image_html = ""
        if sub.image_url:
            image_html = f'<img src="{sub.image_url}" alt="提交图片" class="submission-image" onclick="window.open(this.src)">'
        else:
            image_html = '<div class="no-image">无图片</div>'

        # 备注显示
        note_html = f'<div class="note">"{sub.note}"</div>' if sub.note else ''

        html_content += f"""
            <div class="submission-card">
                {image_html}
                <div class="submission-content">
                    <div class="student-info">学号: {sub.student_no_masked}</div>
                    {score_html}
                    {numeric_html}
                    {note_html}
                    <div class="timestamp">{time_str}</div>
                </div>
            </div>
        """

    html_content += """
        </div>

        <script>
            function updateSort() {
                const sortBy = document.getElementById('sortBy').value;
                const sortOrder = document.getElementById('sortOrder').value;
                const anonymous = document.getElementById('anonymous').value;

                const url = new URL(window.location);
                url.searchParams.set('sort_by', sortBy);
                url.searchParams.set('sort_order', sortOrder);
                url.searchParams.set('anonymous', anonymous);

                window.location.href = url.toString();
            }
        </script>
    </body>
    </html>
    """

    return html_content

@router.get("/students")
def get_students_with_submissions(
    question_id: Optional[int] = Query(None, description="题目ID"),
    class_id: Optional[int] = Query(None, description="班级ID"),
    db: Session = Depends(get_db)
):
    """获取学生及其提交情况"""
    # 如果没有指定班级，使用激活班级
    if not class_id:
        active_class = db.query(Class).filter(Class.is_active == True).first()
        if not active_class:
            return []
        class_id = active_class.id

    # 获取班级下的所有学生
    students_query = db.query(Student).filter(Student.class_id == class_id)
    students = students_query.all()

    result = []
    for student in students:
        # 获取学生的提交记录
        submissions_query = db.query(Submission).filter(
            Submission.student_id == student.id,
            Submission.visible == True
        )

        if question_id:
            submissions_query = submissions_query.filter(Submission.question_id == question_id)

        submissions = submissions_query.all()

        # 构建学生信息
        student_data = {
            "id": student.id,
            "student_no": student.student_no,
            "name": student.name,
            "class_name": student.class_.name,
            "submissions": []
        }

        # 添加提交信息
        for submission in submissions:
            submission_data = {
                "id": submission.id,
                "question_title": submission.question.title,
                "question_id": submission.question_id,
                "score": submission.score,
                "comment": submission.comment,
                "created_at": submission.created_at,
                "has_image": bool(submission.image_path),
                "image_url": get_image_url(submission.image_path) if submission.image_path else None,
                "thumb_url": get_image_url(submission.thumb_path) if submission.thumb_path else None,
                "numeric": json.loads(submission.numeric_json) if submission.numeric_json else None,
                "text_content": submission.text_content,
                "note": submission.note
            }
            student_data["submissions"].append(submission_data)

        result.append(student_data)

    return result

@router.get("/students/view", response_class=HTMLResponse)
def get_students_view(
    question_id: Optional[int] = Query(None, description="题目ID"),
    class_id: Optional[int] = Query(None, description="班级ID"),
    db: Session = Depends(get_db)
):
    """学生提交情况展示页面"""
    # 获取学生数据
    students_data = get_students_with_submissions(question_id, class_id, db)

    # 获取班级和题目信息
    if not class_id:
        active_class = db.query(Class).filter(Class.is_active == True).first()
        class_name = active_class.name if active_class else "未知班级"
    else:
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        class_name = class_obj.name if class_obj else "未知班级"

    question_title = "所有实验"
    if question_id:
        question = db.query(Question).filter(Question.id == question_id).first()
        question_title = question.title if question else "未知实验"

    # 生成HTML
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>学生提交情况 - {class_name}</title>
        <style>
            * {{ margin: 0; padding: 0; box-sizing: border-box; }}
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background-color: #f5f5f5;
                line-height: 1.6;
                padding: 20px;
            }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 15px;
                margin-bottom: 30px;
                text-align: center;
            }}
            .header h1 {{ font-size: 2.5em; margin-bottom: 10px; }}
            .header p {{ font-size: 1.2em; opacity: 0.9; }}
            .student-card {{
                background: white;
                border-radius: 15px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                margin-bottom: 20px;
                overflow: hidden;
                transition: transform 0.3s;
            }}
            .student-card:hover {{ transform: translateY(-5px); }}
            .student-header {{
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                color: white;
                padding: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }}
            .student-info h3 {{ font-size: 1.5em; margin-bottom: 5px; }}
            .student-info p {{ opacity: 0.9; }}
            .submission-count {{
                background: rgba(255,255,255,0.2);
                padding: 10px 15px;
                border-radius: 25px;
                font-weight: bold;
            }}
            .submissions-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                padding: 20px;
            }}
            .submission-item {{
                border: 1px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
                background: #fafafa;
            }}
            .submission-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
            }}
            .submission-title {{ font-weight: bold; color: #333; }}
            .submission-score {{
                background: #28a745;
                color: white;
                padding: 4px 8px;
                border-radius: 15px;
                font-size: 0.9em;
            }}
            .submission-image {{
                width: 100%;
                max-width: 200px;
                height: auto;
                border-radius: 8px;
                margin: 10px 0;
                cursor: pointer;
                transition: transform 0.3s;
            }}
            .submission-image:hover {{ transform: scale(1.05); }}
            .submission-data {{
                background: #e3f2fd;
                padding: 10px;
                border-radius: 5px;
                margin: 10px 0;
                font-family: monospace;
            }}
            .submission-text {{
                background: #f0f8ff;
                padding: 10px;
                border-radius: 5px;
                margin: 10px 0;
                border-left: 4px solid #2196f3;
            }}
            .submission-note {{
                color: #666;
                font-style: italic;
                margin-top: 10px;
            }}
            .no-submissions {{
                text-align: center;
                color: #999;
                padding: 40px;
                font-style: italic;
            }}
            .back-link {{
                text-align: center;
                margin: 30px 0;
            }}
            .back-link a {{
                color: #667eea;
                text-decoration: none;
                font-weight: bold;
                font-size: 1.1em;
            }}
            .back-link a:hover {{ text-decoration: underline; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📊 学生提交情况</h1>
                <p>{class_name} - {question_title}</p>
            </div>
    """

    if not students_data:
        html_content += """
            <div class="no-submissions">
                <h2>暂无学生数据</h2>
                <p>该班级暂无学生或提交记录</p>
            </div>
        """
    else:
        for student in students_data:
            submission_count = len(student["submissions"])
            html_content += f"""
            <div class="student-card">
                <div class="student-header">
                    <div class="student-info">
                        <h3>{student["name"]}</h3>
                        <p>学号: {student["student_no"]}</p>
                    </div>
                    <div class="submission-count">
                        {submission_count} 个提交
                    </div>
                </div>
            """

            if student["submissions"]:
                html_content += '<div class="submissions-grid">'
                for submission in student["submissions"]:
                    html_content += f"""
                    <div class="submission-item">
                        <div class="submission-header">
                            <div class="submission-title">{submission["question_title"]}</div>
                            {f'<div class="submission-score">{submission["score"]}分</div>' if submission["score"] is not None else ''}
                        </div>
                    """

                    if submission["thumb_url"]:
                        html_content += f"""
                        <img src="{submission["thumb_url"]}"
                             class="submission-image"
                             onclick="window.open('{submission["image_url"]}', '_blank')"
                             alt="提交图片">
                        """

                    if submission["numeric"]:
                        html_content += f"""
                        <div class="submission-data">
                            数据: {', '.join(map(str, submission["numeric"]))}
                        </div>
                        """

                    if submission["text_content"]:
                        html_content += f"""
                        <div class="submission-text">
                            {submission["text_content"]}
                        </div>
                        """

                    if submission["note"]:
                        html_content += f"""
                        <div class="submission-note">
                            备注: {submission["note"]}
                        </div>
                        """

                    html_content += "</div>"

                html_content += "</div>"
            else:
                html_content += """
                <div class="no-submissions">
                    <p>该学生暂无提交记录</p>
                </div>
                """

            html_content += "</div>"

    html_content += """
            <div class="back-link">
                <a href="/">← 返回首页</a> |
                <a href="/public/showcase/view">作业展示</a> |
                <a href="/static/admin.html">管理端</a>
            </div>
        </div>
    </body>
    </html>
    """

    return html_content
