// 全局变量
let authToken = localStorage.getItem('authToken');
let currentUser = null;

// API 基础 URL
const API_BASE = '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (authToken) {
        checkAuth();
    }
    
    // 绑定登录表单
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
});

// 显示提示信息
function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    alertContainer.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    try {
        const response = await fetch(`${API_BASE}/admin/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password })
        });
        
        if (response.ok) {
            const data = await response.json();
            authToken = data.access_token;
            localStorage.setItem('authToken', authToken);
            showLoginSuccess();
            loadDashboard();
        } else {
            const error = await response.json();
            showAlert(error.detail || '登录失败', 'error');
        }
    } catch (error) {
        showAlert('网络错误，请重试', 'error');
    }
}

// 显示登录成功
function showLoginSuccess() {
    document.getElementById('loginSection').style.display = 'none';
    document.getElementById('adminPanel').style.display = 'block';
    document.getElementById('loginStatus').innerHTML = `
        <span>已登录</span>
        <button class="btn btn-danger" onclick="logout()" style="margin-left: 10px;">退出</button>
    `;
}

// 检查认证状态
async function checkAuth() {
    try {
        const response = await fetch(`${API_BASE}/admin/me`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            currentUser = await response.json();
            showLoginSuccess();
            loadDashboard();
        } else {
            logout();
        }
    } catch (error) {
        logout();
    }
}

// 退出登录
function logout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    document.getElementById('loginSection').style.display = 'block';
    document.getElementById('adminPanel').style.display = 'none';
    document.getElementById('loginStatus').innerHTML = '';
}

// 切换标签页
function showTab(tabName) {
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 移除所有标签的激活状态
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // 显示选中的标签内容
    document.getElementById(tabName).classList.add('active');
    
    // 激活选中的标签
    event.target.classList.add('active');
    
    // 加载对应的数据
    switch(tabName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'classes':
            loadClasses();
            break;
        case 'students':
            loadStudents();
            break;
        case 'questions':
            loadQuestions();
            break;
        case 'answers':
            loadAnswers();
            break;
        case 'display':
            loadDisplay();
            break;
    }
}

// 加载仪表板数据
async function loadDashboard() {
    try {
        const [classesRes, studentsRes, questionsRes, answersRes] = await Promise.all([
            fetch(`${API_BASE}/admin/classes`, { headers: { 'Authorization': `Bearer ${authToken}` } }),
            fetch(`${API_BASE}/admin/students`, { headers: { 'Authorization': `Bearer ${authToken}` } }),
            fetch(`${API_BASE}/admin/questions`, { headers: { 'Authorization': `Bearer ${authToken}` } }),
            fetch(`${API_BASE}/admin/submissions`, { headers: { 'Authorization': `Bearer ${authToken}` } })
        ]);

        const classes = await classesRes.json();
        const students = await studentsRes.json();
        const questions = await questionsRes.json();
        const answers = await answersRes.json();
        
        const statsContainer = document.getElementById('statsContainer');
        statsContainer.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${classes.length}</div>
                <div class="stat-label">班级数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${students.length}</div>
                <div class="stat-label">学生数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${questions.length}</div>
                <div class="stat-label">题目数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${answers.length}</div>
                <div class="stat-label">答案数量</div>
            </div>
        `;
    } catch (error) {
        showAlert('加载仪表板数据失败', 'error');
    }
}

// 加载班级列表
async function loadClasses() {
    try {
        const response = await fetch(`${API_BASE}/admin/classes`, {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });
        
        if (response.ok) {
            const classes = await response.json();
            const tbody = document.querySelector('#classesTable tbody');
            tbody.innerHTML = classes.map(cls => `
                <tr>
                    <td>${cls.id}</td>
                    <td>${cls.name}</td>
                    <td>${cls.code || '-'}</td>
                    <td><span class="status-badge ${cls.is_active ? 'status-active' : 'status-inactive'}">
                        ${cls.is_active ? '激活' : '未激活'}
                    </span></td>
                    <td>${cls.student_count || 0}</td>
                    <td>${new Date(cls.created_at).toLocaleString()}</td>
                    <td>
                        <button class="btn" onclick="editClass(${cls.id})">编辑</button>
                        ${!cls.is_active ? `<button class="btn btn-success" onclick="activateClass(${cls.id})">激活</button>` : ''}
                        <button class="btn btn-danger" onclick="deleteClass(${cls.id})">删除</button>
                    </td>
                </tr>
            `).join('');
            
            // 更新班级选择器
            updateClassSelectors(classes);
        }
    } catch (error) {
        showAlert('加载班级列表失败', 'error');
    }
}

// 更新班级选择器
function updateClassSelectors(classes) {
    const selectors = ['studentClassFilter', 'taskClassFilter', 'submissionClassFilter'];
    selectors.forEach(selectorId => {
        const selector = document.getElementById(selectorId);
        if (selector) {
            const currentValue = selector.value;
            selector.innerHTML = '<option value="">所有班级</option>' + 
                classes.map(cls => `<option value="${cls.id}">${cls.name}</option>`).join('');
            selector.value = currentValue;
        }
    });
}

// API 请求辅助函数
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
            ...options.headers
        }
    };
    
    const response = await fetch(`${API_BASE}${url}`, { ...defaultOptions, ...options });
    
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || '请求失败');
    }
    
    return response.json();
}

// 创建班级模态框
function showCreateClassModal() {
    const modal = createModal('创建班级', `
        <form id="createClassForm">
            <div class="form-group">
                <label>班级名称</label>
                <input type="text" id="className" required>
            </div>
            <div class="form-group">
                <label>班级代码</label>
                <input type="text" id="classCode">
            </div>
            <button type="submit" class="btn">创建</button>
            <button type="button" class="btn btn-danger" onclick="closeModal()">取消</button>
        </form>
    `);
    
    document.getElementById('createClassForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        try {
            await apiRequest('/admin/classes', {
                method: 'POST',
                body: JSON.stringify({
                    name: document.getElementById('className').value,
                    code: document.getElementById('classCode').value || null
                })
            });
            showAlert('班级创建成功');
            closeModal();
            loadClasses();
        } catch (error) {
            showAlert(error.message, 'error');
        }
    });
}

// 创建模态框辅助函数
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>${title}</h2>
            ${content}
        </div>
    `;
    document.body.appendChild(modal);
    return modal;
}

// 关闭模态框
function closeModal() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => modal.remove());
}

// 激活班级
async function activateClass(classId) {
    try {
        await apiRequest(`/admin/classes/${classId}/activate`, { method: 'PATCH' });
        showAlert('班级激活成功');
        loadClasses();
    } catch (error) {
        showAlert(error.message, 'error');
    }
}

// 删除班级
async function deleteClass(classId) {
    if (confirm('确定要删除这个班级吗？这将删除班级下的所有数据。')) {
        try {
            await apiRequest(`/admin/classes/${classId}`, { method: 'DELETE' });
            showAlert('班级删除成功');
            loadClasses();
        } catch (error) {
            showAlert(error.message, 'error');
        }
    }
}

// 加载学生列表
async function loadStudents() {
    try {
        const classId = document.getElementById('studentClassFilter').value;
        const url = classId ? `/admin/students?class_id=${classId}` : '/admin/students';
        const students = await apiRequest(url);
        
        const tbody = document.querySelector('#studentsTable tbody');
        tbody.innerHTML = students.map(student => `
            <tr>
                <td>${student.id}</td>
                <td>${student.student_no}</td>
                <td>${student.name}</td>
                <td>${student.class_name || '-'}</td>
                <td>${student.email || '-'}</td>
                <td>${student.submission_count || 0}</td>
                <td>
                    <button class="btn" onclick="editStudent(${student.id})">编辑</button>
                    <button class="btn btn-danger" onclick="deleteStudent(${student.id})">删除</button>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        showAlert('加载学生列表失败', 'error');
    }
}

// 添加学生模态框
function showAddStudentModal() {
    const modal = createModal('添加学生', `
        <form id="addStudentForm">
            <div class="form-group">
                <label>选择班级</label>
                <select id="studentClassId" required>
                    <option value="">请选择班级</option>
                </select>
            </div>
            <div class="form-group">
                <label>学号</label>
                <input type="text" id="studentNo" required>
            </div>
            <div class="form-group">
                <label>姓名</label>
                <input type="text" id="studentName" required>
            </div>
            <div class="form-group">
                <label>邮箱</label>
                <input type="email" id="studentEmail">
            </div>
            <button type="submit" class="btn">添加</button>
            <button type="button" class="btn btn-danger" onclick="closeModal()">取消</button>
        </form>
    `);

    // 加载班级选项
    loadClassOptions('studentClassId');

    document.getElementById('addStudentForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        try {
            await apiRequest('/admin/students', {
                method: 'POST',
                body: JSON.stringify({
                    class_id: parseInt(document.getElementById('studentClassId').value),
                    student_no: document.getElementById('studentNo').value,
                    name: document.getElementById('studentName').value,
                    email: document.getElementById('studentEmail').value || null
                })
            });
            showAlert('学生添加成功');
            closeModal();
            loadStudents();
        } catch (error) {
            showAlert(error.message, 'error');
        }
    });
}

// 批量导入学生模态框
function showImportStudentsModal() {
    const modal = createModal('批量导入学生', `
        <form id="importStudentsForm">
            <div class="form-group">
                <label>选择班级</label>
                <select id="importClassId" required>
                    <option value="">请选择班级</option>
                </select>
            </div>
            <div class="form-group">
                <label>学生数据 (CSV格式: 学号,姓名,邮箱)</label>
                <textarea id="studentsData" placeholder="2023001,张三,<EMAIL>
2023002,李四,<EMAIL>" required></textarea>
            </div>
            <button type="submit" class="btn">导入</button>
            <button type="button" class="btn btn-danger" onclick="closeModal()">取消</button>
        </form>
    `);

    loadClassOptions('importClassId');

    document.getElementById('importStudentsForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        try {
            const classId = parseInt(document.getElementById('importClassId').value);
            const data = document.getElementById('studentsData').value;
            const students = data.split('\n').map(line => {
                const [student_no, name, email] = line.split(',').map(s => s.trim());
                return { student_no, name, email: email || null };
            }).filter(s => s.student_no && s.name);

            await apiRequest('/admin/students/import', {
                method: 'POST',
                body: JSON.stringify({ class_id: classId, students })
            });
            showAlert(`成功导入 ${students.length} 个学生`);
            closeModal();
            loadStudents();
        } catch (error) {
            showAlert(error.message, 'error');
        }
    });
}

// 加载班级选项
async function loadClassOptions(selectId) {
    try {
        const classes = await apiRequest('/admin/classes');
        const select = document.getElementById(selectId);
        select.innerHTML = '<option value="">请选择班级</option>' +
            classes.map(cls => `<option value="${cls.id}">${cls.name}</option>`).join('');
    } catch (error) {
        showAlert('加载班级选项失败', 'error');
    }
}

// 删除学生
async function deleteStudent(studentId) {
    if (!confirm('确定要删除这个学生吗？此操作不可恢复。')) {
        return;
    }

    try {
        await apiRequest(`/admin/students/${studentId}`, {
            method: 'DELETE'
        });
        showAlert('学生删除成功');
        loadStudents();
    } catch (error) {
        showAlert(error.message, 'error');
    }
}

// 编辑学生
async function editStudent(studentId) {
    // TODO: 实现编辑学生功能
    showAlert('编辑功能开发中', 'info');
}

// 加载题目列表
async function loadQuestions() {
    try {
        const classId = document.getElementById('questionClassFilter').value;
        const url = classId ? `/admin/questions?class_id=${classId}` : '/admin/questions';
        const questions = await apiRequest(url);

        const tbody = document.querySelector('#questionsTable tbody');
        tbody.innerHTML = questions.map(question => `
            <tr>
                <td>${question.id}</td>
                <td>${question.title}</td>
                <td>${question.class_name || '-'}</td>
                <td>${getQuestionTypeText(question.question_type)}</td>
                <td><span class="status-badge ${question.is_published ? 'status-open' : 'status-closed'}">
                    ${question.is_published ? '已发布' : '未发布'}
                </span></td>
                <td><span class="status-badge ${question.is_open ? 'status-open' : 'status-closed'}">
                    ${question.is_open ? '开放' : '关闭'}
                </span></td>
                <td>${question.submission_count || 0}</td>
                <td>
                    <button class="btn" onclick="editQuestion(${question.id})">编辑</button>
                    <button class="btn" onclick="publishQuestion(${question.id})">发布</button>
                    <button class="btn ${question.is_open ? 'btn-danger' : 'btn-success'}"
                            onclick="toggleQuestion(${question.id}, ${!question.is_open})">
                        ${question.is_open ? '关闭' : '开放'}
                    </button>
                    <button class="btn" onclick="generateCode(${question.id})">生成代码</button>
                    <button class="btn btn-danger" onclick="deleteQuestion(${question.id})">删除</button>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        showAlert('加载题目列表失败', 'error');
    }
}

function getQuestionTypeText(type) {
    const types = {
        'submit_program': '提交程序',
        'submit_data': '提交数据',
        'submit_result': '提交结果'
    };
    return types[type] || type;
}

// 创建题目模态框
function showCreateQuestionModal() {
    const modal = createModal('创建题目', `
        <form id="createQuestionForm">
            <div class="form-group">
                <label>选择班级</label>
                <select id="questionClassId" required>
                    <option value="">请选择班级</option>
                </select>
            </div>
            <div class="form-group">
                <label>题目名称</label>
                <input type="text" id="questionTitle" required>
            </div>
            <div class="form-group">
                <label>题目描述</label>
                <textarea id="questionDescription" rows="4" placeholder="详细描述题目要求和计算内容"></textarea>
            </div>
            <div class="form-group">
                <label>题目类型</label>
                <select id="questionType">
                    <option value="submit_result">提交结果</option>
                    <option value="submit_data">提交数据</option>
                    <option value="submit_program">提交程序</option>
                </select>
            </div>
            <div class="form-group">
                <label>提交类型</label>
                <select id="submissionType">
                    <option value="image_data">图片+数据</option>
                    <option value="image_only">仅图片</option>
                    <option value="data_only">仅数据</option>
                    <option value="text">文本</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="questionIsOpen" checked> 立即开放提交
                </label>
            </div>
            <button type="submit" class="btn">创建题目</button>
            <button type="button" class="btn btn-danger" onclick="closeModal()">取消</button>
        </form>
    `);

    loadClassOptions('questionClassId');

    document.getElementById('createQuestionForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        try {
            await apiRequest('/admin/questions', {
                method: 'POST',
                body: JSON.stringify({
                    class_id: parseInt(document.getElementById('questionClassId').value),
                    title: document.getElementById('questionTitle').value,
                    description: document.getElementById('questionDescription').value || null,
                    question_type: document.getElementById('questionType').value,
                    submission_type: document.getElementById('submissionType').value,
                    is_open: document.getElementById('questionIsOpen').checked
                })
            });
            showAlert('题目创建成功');
            closeModal();
            loadQuestions();
        } catch (error) {
            showAlert(error.message, 'error');
        }
    });
}

// 切换任务状态
async function toggleTask(taskId, isOpen) {
    try {
        await apiRequest(`/admin/tasks/${taskId}`, {
            method: 'PATCH',
            body: JSON.stringify({ is_open: isOpen })
        });
        showAlert(`任务${isOpen ? '开放' : '关闭'}成功`);
        loadTasks();
    } catch (error) {
        showAlert(error.message, 'error');
    }
}

// 删除任务
async function deleteTask(taskId) {
    if (!confirm('确定要删除这个任务吗？此操作不可恢复。')) {
        return;
    }

    try {
        await apiRequest(`/admin/tasks/${taskId}`, {
            method: 'DELETE'
        });
        showAlert('任务删除成功');
        loadTasks();
    } catch (error) {
        showAlert(error.message, 'error');
    }
}

// 题目相关功能
async function editQuestion(questionId) {
    // TODO: 实现编辑题目功能
    showAlert('编辑功能开发中', 'info');
}

async function toggleQuestion(questionId, isOpen) {
    try {
        await apiRequest(`/admin/questions/${questionId}`, {
            method: 'PATCH',
            body: JSON.stringify({ is_open: isOpen })
        });
        showAlert(`题目${isOpen ? '开放' : '关闭'}成功`);
        loadQuestions();
    } catch (error) {
        showAlert(error.message, 'error');
    }
}

async function deleteQuestion(questionId) {
    if (!confirm('确定要删除这个题目吗？此操作不可恢复。')) {
        return;
    }

    try {
        await apiRequest(`/admin/questions/${questionId}`, {
            method: 'DELETE'
        });
        showAlert('题目删除成功');
        loadQuestions();
    } catch (error) {
        showAlert(error.message, 'error');
    }
}

// 发布题目功能
async function publishQuestion(questionId) {
    try {
        const classes = await apiRequest('/admin/classes');

        const modal = createModal('发布题目', `
            <form id="publishForm">
                <div class="form-group">
                    <label>选择发布的班级</label>
                    <div id="classCheckboxes">
                        ${classes.map(cls => `
                            <label style="display: block; margin: 5px 0;">
                                <input type="checkbox" value="${cls.id}" name="publishClasses">
                                ${cls.name}
                            </label>
                        `).join('')}
                    </div>
                </div>
                <button type="submit" class="btn">发布题目</button>
                <button type="button" class="btn btn-danger" onclick="closeModal()">取消</button>
            </form>
        `);

        document.getElementById('publishForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const selectedClasses = Array.from(document.querySelectorAll('input[name="publishClasses"]:checked'))
                .map(cb => parseInt(cb.value));

            if (selectedClasses.length === 0) {
                showAlert('请选择至少一个班级', 'error');
                return;
            }

            try {
                await apiRequest(`/admin/questions/${questionId}/publish`, {
                    method: 'POST',
                    body: JSON.stringify({ class_ids: selectedClasses })
                });
                showAlert('题目发布成功');
                closeModal();
                loadQuestions();
            } catch (error) {
                showAlert(error.message, 'error');
            }
        });
    } catch (error) {
        showAlert('加载班级信息失败', 'error');
    }
}

// 生成学生代码功能
async function generateCode(questionId) {
    try {
        const question = await apiRequest(`/admin/questions/${questionId}`);

        const modal = createModal('生成学生代码', `
            <div>
                <h3>${question.title}</h3>
                <p><strong>题目类型:</strong> ${getQuestionTypeText(question.question_type)}</p>
                <p><strong>描述:</strong> ${question.description || '无'}</p>

                <div class="form-group">
                    <label>生成的Python代码 (学生使用)</label>
                    <textarea id="generatedCode" rows="20" style="width: 100%; font-family: monospace;" readonly></textarea>
                </div>

                <div style="margin-top: 10px;">
                    <button class="btn" onclick="copyCode()">复制代码</button>
                    <button class="btn" onclick="downloadCode(${questionId})">下载文件</button>
                    <button class="btn btn-danger" onclick="closeModal()">关闭</button>
                </div>
            </div>
        `);

        // 生成代码模板
        const code = generateStudentCode(question);
        document.getElementById('generatedCode').value = code;

    } catch (error) {
        showAlert('获取题目信息失败', 'error');
    }
}

function generateStudentCode(question) {
    return `#!/usr/bin/env python3
"""
计算物理作业 - ${question.title}
题目ID: ${question.id}
题目类型: ${getQuestionTypeText(question.question_type)}

题目描述:
${question.description || '请根据课堂要求完成计算'}

使用说明:
1. 修改下面的student_no为你的学号
2. 在指定位置编写计算代码
3. 运行程序自动提交结果到服务器
4. 确保网络连接正常
"""

# 导入必要的库
from physics_submit import submit_answer, submit_plot_and_data, quick_submit
import numpy as np
import matplotlib.pyplot as plt

# ========== 请修改这里的学号 ==========
student_no = "请输入你的学号"  # 例如: "2022062030"
# ====================================

question_id = ${question.id}

def main():
    """主要计算函数"""
    print("📚 ${question.title}")
    print("=" * 50)

    # ========== 在这里编写你的计算代码 ==========

    # 示例代码 - 请根据题目要求修改
    x = np.linspace(0, 2*np.pi, 100)
    y = np.sin(x)  # 请修改为实际的计算公式

    # 创建图形
    plt.figure(figsize=(10, 6))
    plt.plot(x, y, 'b-', linewidth=2)
    plt.title('${question.title} - 学号: ' + student_no)
    plt.xlabel('x')
    plt.ylabel('y')
    plt.grid(True, alpha=0.3)

    # ========================================

    # 提交结果 (根据题目类型选择合适的提交方式)
    print("\\n📤 正在提交答案...")

    ${getSubmissionCode(question.question_type)}

    if result["success"]:
        print("🎉 提交成功! 请在课堂展示页面查看结果")
        print("🌐 展示地址: http://43.155.146.157:30200/public/showcase/view")
    else:
        print("❌ 提交失败:", result.get("error", "未知错误"))
        print("💡 请检查网络连接和学号是否正确")

if __name__ == "__main__":
    main()
`;
}

function getSubmissionCode(questionType) {
    switch(questionType) {
        case 'submit_program':
            return `    # 提交程序类型 - 包含完整计算过程
    result = submit_answer(student_no, question_id,
                          image_data='current',
                          numeric_data=y.tolist(),
                          text_data="程序计算过程和结果分析",
                          note="完整程序提交")`;
        case 'submit_data':
            return `    # 提交数据类型 - 主要提交计算数据
    result = submit_answer(student_no, question_id,
                          numeric_data=y.tolist(),
                          note="数据计算结果")`;
        case 'submit_result':
        default:
            return `    # 提交结果类型 - 图形和关键数据
    result = submit_plot_and_data(student_no, question_id,
                                 x.tolist(), y.tolist(),
                                 "${question.title || '计算结果'}")`;
    }
}

function copyCode() {
    const codeArea = document.getElementById('generatedCode');
    codeArea.select();
    document.execCommand('copy');
    showAlert('代码已复制到剪贴板');
}

function downloadCode(questionId) {
    const code = document.getElementById('generatedCode').value;
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `physics_question_${questionId}.py`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    showAlert('代码文件已下载');
}

// 加载提交列表
async function loadSubmissions() {
    try {
        const classId = document.getElementById('submissionClassFilter').value;
        const taskId = document.getElementById('submissionTaskFilter').value;

        let url = '/admin/submissions?';
        if (classId) url += `class_id=${classId}&`;
        if (taskId) url += `question_id=${taskId}&`;

        const submissions = await apiRequest(url);

        const tbody = document.querySelector('#submissionsTable tbody');
        tbody.innerHTML = submissions.map(submission => `
            <tr>
                <td>${submission.id}</td>
                <td>${submission.student_no}</td>
                <td>${submission.student_name || '-'}</td>
                <td>${submission.question_title || '-'}</td>
                <td>${new Date(submission.created_at).toLocaleString()}</td>
                <td>${submission.score || '-'}</td>
                <td><span class="status-badge ${submission.visible ? 'status-active' : 'status-inactive'}">
                    ${submission.visible ? '可见' : '隐藏'}
                </span></td>
                <td>
                    <button class="btn" onclick="viewSubmission(${submission.id})">查看</button>
                    <button class="btn" onclick="gradeSubmission(${submission.id})">评分</button>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        showAlert('加载提交列表失败', 'error');
    }
}

// 查看提交详情
async function viewSubmission(submissionId) {
    try {
        const submission = await apiRequest(`/admin/submissions/${submissionId}`);

        let imageHtml = '';
        if (submission.image_url) {
            imageHtml = `
                <div class="form-group">
                    <label>提交图片</label>
                    <div>
                        <img src="${submission.image_url}" alt="提交图片" style="max-width: 100%; max-height: 400px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                </div>
            `;
        }

        let numericHtml = '';
        if (submission.numeric && Array.isArray(submission.numeric)) {
            numericHtml = `
                <div class="form-group">
                    <label>数值数据</label>
                    <div style="background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace;">
                        ${submission.numeric.join(', ')}
                    </div>
                </div>
            `;
        }

        const modal = createModal('提交详情', `
            <div class="submission-detail">
                <div class="form-group">
                    <label>学号</label>
                    <div>${submission.student_no}</div>
                </div>
                <div class="form-group">
                    <label>姓名</label>
                    <div>${submission.student_name || '-'}</div>
                </div>
                <div class="form-group">
                    <label>提交时间</label>
                    <div>${new Date(submission.created_at).toLocaleString()}</div>
                </div>
                <div class="form-group">
                    <label>备注</label>
                    <div>${submission.note || '-'}</div>
                </div>
                ${imageHtml}
                ${numericHtml}
                <div class="form-group">
                    <label>当前评分</label>
                    <div>${submission.score || '未评分'}</div>
                </div>
                <div class="form-group">
                    <label>评语</label>
                    <div>${submission.comment || '无'}</div>
                </div>
                <div class="form-group">
                    <label>可见性</label>
                    <div>${submission.visible ? '可见' : '隐藏'}</div>
                </div>
                <button type="button" class="btn" onclick="gradeSubmission(${submissionId}); closeModal();">评分</button>
                <button type="button" class="btn btn-danger" onclick="closeModal()">关闭</button>
            </div>
        `);
    } catch (error) {
        showAlert('加载提交详情失败: ' + error.message, 'error');
    }
}

// 评分提交
async function gradeSubmission(submissionId) {
    try {
        const submission = await apiRequest(`/admin/submissions/${submissionId}`);

        const modal = createModal('评分提交', `
            <form id="gradeForm">
                <div class="form-group">
                    <label>学生信息</label>
                    <div>${submission.student_no} - ${submission.student_name || '未知'}</div>
                </div>
                <div class="form-group">
                    <label>评分 (0-100)</label>
                    <input type="number" id="score" min="0" max="100" value="${submission.score || ''}" placeholder="请输入分数">
                </div>
                <div class="form-group">
                    <label>评语</label>
                    <textarea id="comment" placeholder="请输入评语">${submission.comment || ''}</textarea>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="visible" ${submission.visible ? 'checked' : ''}> 公开可见
                    </label>
                </div>
                <button type="submit" class="btn">保存</button>
                <button type="button" class="btn btn-danger" onclick="closeModal()">取消</button>
            </form>
        `);

        document.getElementById('gradeForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            try {
                const score = document.getElementById('score').value;
                const comment = document.getElementById('comment').value;
                const visible = document.getElementById('visible').checked;

                await apiRequest(`/admin/submissions/${submissionId}`, {
                    method: 'PATCH',
                    body: JSON.stringify({
                        score: score ? parseFloat(score) : null,
                        comment: comment || null,
                        visible: visible
                    })
                });

                showAlert('评分保存成功');
                closeModal();
                loadSubmissions();
            } catch (error) {
                showAlert('保存评分失败: ' + error.message, 'error');
            }
        });
    } catch (error) {
        showAlert('加载提交信息失败: ' + error.message, 'error');
    }
}

// 答案管理功能
async function loadAnswers() {
    try {
        const classId = document.getElementById('answerClassFilter').value;
        const questionId = document.getElementById('answerQuestionFilter').value;

        let url = '/admin/submissions?';
        if (classId) url += `class_id=${classId}&`;
        if (questionId) url += `question_id=${questionId}&`;

        const answers = await apiRequest(url);

        const tbody = document.querySelector('#answersTable tbody');
        tbody.innerHTML = answers.map(answer => `
            <tr>
                <td>${answer.id}</td>
                <td>${answer.student_no}</td>
                <td>${answer.student_name || '-'}</td>
                <td>${answer.question_title || '-'}</td>
                <td>${new Date(answer.created_at).toLocaleString()}</td>
                <td>${answer.score || '-'}</td>
                <td><span class="status-badge ${answer.visible ? 'status-active' : 'status-inactive'}">
                    ${answer.visible ? '可见' : '隐藏'}
                </span></td>
                <td>
                    <button class="btn" onclick="viewAnswer(${answer.id})">查看</button>
                    <button class="btn" onclick="gradeAnswer(${answer.id})">评分</button>
                </td>
            </tr>
        `).join('');
    } catch (error) {
        showAlert('加载答案列表失败', 'error');
    }
}

async function viewAnswer(answerId) {
    return viewSubmission(answerId); // 复用原有功能
}

async function gradeAnswer(answerId) {
    return gradeSubmission(answerId); // 复用原有功能
}

// ==================== 课堂展示功能 ====================

async function refreshDisplay() {
    // 刷新课堂展示
    try {
        await loadDisplayClasses();
        await loadDisplayQuestions();
        updateDisplay();
        showAlert('展示数据已刷新', 'success');
    } catch (error) {
        console.error('刷新展示失败:', error);
        showAlert('刷新展示失败', 'error');
    }
}

function openDisplayWindow() {
    // 打开课堂展示窗口，传递当前选择的班级和题目参数
    const classId = document.getElementById('displayClassFilter')?.value;
    const questionId = document.getElementById('displayQuestionFilter')?.value;

    let displayUrl = `${window.location.origin}/static/classroom_display.html`;

    // 如果已经选择了班级和题目，将它们作为URL参数传递
    if (classId && questionId) {
        displayUrl += `?class_id=${classId}&question_id=${questionId}`;
        console.log('打开展示窗口，传递参数:', { classId, questionId });
    } else {
        console.log('打开展示窗口，未选择班级或题目');
    }

    window.open(displayUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

async function loadDisplayClasses() {
    // 加载展示用班级列表
    try {
        const response = await fetch(`${API_BASE}/public/classes`);
        const classes = await response.json();

        const select = document.getElementById('displayClassFilter');
        if (select) {
            select.innerHTML = '<option value="">请选择班级</option>' +
                classes.map(cls => `<option value="${cls.id}">${cls.name}</option>`).join('');
        }
    } catch (error) {
        console.error('加载展示班级失败:', error);
    }
}

async function loadDisplayQuestions() {
    // 加载展示用题目列表
    try {
        const response = await fetch(`${API_BASE}/public/questions`);
        const questions = await response.json();

        const select = document.getElementById('displayQuestionFilter');
        if (select) {
            select.innerHTML = '<option value="">请选择题目</option>' +
                questions.map(q => `<option value="${q.id}">${q.title}</option>`).join('');
        }
    } catch (error) {
        console.error('加载展示题目失败:', error);
    }
}

function updateDisplay() {
    // 更新展示内容
    const classId = document.getElementById('displayClassFilter')?.value;
    const questionId = document.getElementById('displayQuestionFilter')?.value;
    const mode = document.getElementById('displayMode')?.value || 'grid';

    console.log('更新展示:', { classId, questionId, mode });

    // 这里可以添加实际的展示更新逻辑
    // 目前只是记录选择的参数
}

// 课堂展示功能 (保留原有的loadDisplay函数作为备用)
async function loadDisplay() {
    try {
        // 加载班级和题目选项
        const classes = await apiRequest('/admin/classes');
        const questions = await apiRequest('/admin/questions');

        const classFilter = document.getElementById('displayClassFilter');
        const questionFilter = document.getElementById('displayQuestionFilter');

        classFilter.innerHTML = '<option value="">请选择班级</option>' +
            classes.map(cls => `<option value="${cls.id}">${cls.name}</option>`).join('');

        questionFilter.innerHTML = '<option value="">请选择题目</option>' +
            questions.map(q => `<option value="${q.id}">${q.title}</option>`).join('');

    } catch (error) {
        showAlert('加载展示数据失败', 'error');
    }
}

// 代码生成器模态框
function showCodeGeneratorModal() {
    const modal = createModal('AI代码生成器', `
        <div>
            <p>选择一个题目来生成学生使用的Python代码模板</p>
            <div class="form-group">
                <label>选择题目</label>
                <select id="codeGenQuestionId">
                    <option value="">请选择题目</option>
                </select>
            </div>
            <button class="btn" onclick="generateSelectedCode()">生成代码</button>
            <button class="btn btn-danger" onclick="closeModal()">关闭</button>
        </div>
    `);

    // 加载题目列表
    loadQuestionsForCodeGen();
}

async function loadQuestionsForCodeGen() {
    try {
        const questions = await apiRequest('/admin/questions');
        const select = document.getElementById('codeGenQuestionId');
        select.innerHTML = '<option value="">请选择题目</option>' +
            questions.map(q => `<option value="${q.id}">${q.title} (${getQuestionTypeText(q.question_type)})</option>`).join('');
    } catch (error) {
        showAlert('加载题目列表失败', 'error');
    }
}

async function generateSelectedCode() {
    const questionId = document.getElementById('codeGenQuestionId').value;
    if (!questionId) {
        showAlert('请选择一个题目', 'error');
        return;
    }

    closeModal();
    generateCode(parseInt(questionId));
}
