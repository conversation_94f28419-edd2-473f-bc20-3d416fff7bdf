<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理端 - 学生作业提交系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-radius: 10px;
            transition: all 0.3s;
        }
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        .nav-tab:hover:not(.active) {
            background: #f0f0f0;
        }
        .tab-content {
            display: none;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tab-content.active { display: block; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-group textarea { height: 80px; resize: vertical; }
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background 0.3s;
        }
        .btn:hover { background: #5a6fd8; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .table tr:hover { background: #f5f5f5; }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-open { background: #d1ecf1; color: #0c5460; }
        .status-closed { background: #fff3cd; color: #856404; }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover { color: black; }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 管理端</h1>
            <p>学生作业提交系统管理界面</p>
            <div id="loginStatus" style="margin-top: 10px;"></div>
        </div>

        <div id="loginSection">
            <div class="card">
                <h2>管理员登录</h2>
                <form id="loginForm">
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" id="username" value="admin" required>
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" id="password" value="admin123" required>
                    </div>
                    <button type="submit" class="btn">登录</button>
                </form>
            </div>
        </div>

        <div id="adminPanel" style="display: none;">
            <div class="nav-tabs">
                <div class="nav-tab active" onclick="showTab('dashboard')">仪表板</div>
                <div class="nav-tab" onclick="showTab('classes')">班级管理</div>
                <div class="nav-tab" onclick="showTab('students')">学生管理</div>
                <div class="nav-tab" onclick="showTab('questions')">题目管理</div>
                <div class="nav-tab" onclick="showTab('answers')">答案管理</div>
                <div class="nav-tab" onclick="showTab('display')">课堂展示</div>
            </div>

            <!-- 仪表板 -->
            <div id="dashboard" class="tab-content active">
                <h2>系统概览</h2>
                <div class="stats" id="statsContainer">
                    <!-- 统计数据将在这里动态加载 -->
                </div>
            </div>

            <!-- 班级管理 -->
            <div id="classes" class="tab-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2>班级管理</h2>
                    <button class="btn" onclick="showCreateClassModal()">创建班级</button>
                </div>
                <table class="table" id="classesTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>班级名称</th>
                            <th>班级代码</th>
                            <th>状态</th>
                            <th>学生数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

            <!-- 学生管理 -->
            <div id="students" class="tab-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2>学生管理</h2>
                    <div>
                        <button class="btn" onclick="showAddStudentModal()">添加学生</button>
                        <button class="btn btn-success" onclick="showImportStudentsModal()">批量导入</button>
                    </div>
                </div>
                <div class="form-group" style="max-width: 300px;">
                    <label>选择班级</label>
                    <select id="studentClassFilter" onchange="loadStudents()">
                        <option value="">所有班级</option>
                    </select>
                </div>
                <table class="table" id="studentsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>学号</th>
                            <th>姓名</th>
                            <th>班级</th>
                            <th>邮箱</th>
                            <th>提交数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

            <!-- 题目管理 -->
            <div id="questions" class="tab-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2>题目管理</h2>
                    <div>
                        <button class="btn" onclick="showCreateQuestionModal()">创建题目</button>
                        <button class="btn" onclick="showCodeGeneratorModal()">生成学生代码</button>
                    </div>
                </div>
                <div class="form-group" style="max-width: 300px;">
                    <label>选择班级</label>
                    <select id="questionClassFilter" onchange="loadQuestions()">
                        <option value="">所有班级</option>
                    </select>
                </div>
                <table class="table" id="questionsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>题目名称</th>
                            <th>班级</th>
                            <th>题目类型</th>
                            <th>发布状态</th>
                            <th>开放状态</th>
                            <th>答案数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

            <!-- 答案管理 -->
            <div id="answers" class="tab-content">
                <h2>答案管理</h2>
                <div class="grid" style="margin-bottom: 20px;">
                    <div class="form-group">
                        <label>选择班级</label>
                        <select id="answerClassFilter" onchange="loadAnswers()">
                            <option value="">所有班级</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>选择题目</label>
                        <select id="answerQuestionFilter" onchange="loadAnswers()">
                            <option value="">所有题目</option>
                        </select>
                    </div>
                </div>
                <table class="table" id="answersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>学号</th>
                            <th>姓名</th>
                            <th>题目</th>
                            <th>提交时间</th>
                            <th>分数</th>
                            <th>可见</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>

            <!-- 课堂展示 -->
            <div id="display" class="tab-content">
                <h2>课堂展示</h2>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <div>
                        <button class="btn" onclick="refreshDisplay()">刷新展示</button>
                        <button class="btn" onclick="openDisplayWindow()">打开展示窗口</button>
                    </div>
                </div>
                <div class="grid" style="margin-bottom: 20px;">
                    <div class="form-group">
                        <label>选择班级</label>
                        <select id="displayClassFilter" onchange="updateDisplay()">
                            <option value="">请选择班级</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>选择题目</label>
                        <select id="displayQuestionFilter" onchange="updateDisplay()">
                            <option value="">请选择题目</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>展示模式</label>
                        <select id="displayMode" onchange="updateDisplay()">
                            <option value="grid">网格模式</option>
                            <option value="carousel">轮播模式</option>
                            <option value="comparison">对比模式</option>
                        </select>
                    </div>
                </div>
                <div id="displayPreview" style="border: 1px solid #ddd; padding: 20px; min-height: 400px; background: #f9f9f9;">
                    <p style="text-align: center; color: #666;">请选择班级和题目查看展示预览</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框将在这里添加 -->
    <div id="alertContainer"></div>

    <script src="/static/admin.js"></script>
</body>
</html>
