<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生提交 - 实验作业系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 16px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        .file-upload input[type=file] {
            position: absolute;
            left: -9999px;
        }
        .file-upload-label {
            display: block;
            padding: 12px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .file-upload-label:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        .file-upload-label.has-file {
            border-color: #28a745;
            background: #f8fff9;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            width: 100%;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: bold;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .question-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        .question-info h3 {
            color: #333;
            margin-bottom: 10px;
        }
        .question-info p {
            color: #666;
            line-height: 1.6;
        }
        .submission-type {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #1565c0;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: bold;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 实验作业提交</h1>
            <p>请填写学号并提交您的实验作业</p>
        </div>

        <div id="alertContainer"></div>

        <!-- 选择实验 -->
        <div id="questionSelection">
            <div class="form-group">
                <label>选择实验</label>
                <select id="questionSelect" onchange="loadQuestionInfo()">
                    <option value="">请选择实验...</option>
                </select>
            </div>
        </div>

        <!-- 实验信息 -->
        <div id="questionInfo" style="display: none;">
            <div class="question-info">
                <h3 id="questionTitle"></h3>
                <p id="questionDescription"></p>
                <div id="submissionTypeInfo" class="submission-type"></div>
            </div>
        </div>

        <!-- 提交表单 -->
        <div id="submissionForm" style="display: none;">
            <form id="submitForm">
                <div class="form-group">
                    <label>学号 *</label>
                    <input type="text" id="studentNo" required placeholder="请输入您的学号">
                </div>

                <!-- 图片上传 -->
                <div id="imageUpload" class="form-group" style="display: none;">
                    <label>上传图片</label>
                    <div class="file-upload">
                        <input type="file" id="imageFile" accept="image/*">
                        <label for="imageFile" class="file-upload-label" id="imageLabel">
                            📷 点击选择图片文件 (支持 JPG, PNG)
                        </label>
                    </div>
                </div>

                <!-- 数据输入 -->
                <div id="dataInput" class="form-group" style="display: none;">
                    <label>实验数据</label>
                    <textarea id="numericData" placeholder="请输入实验数据，多个数值用逗号分隔，如：9.79, 9.81, 9.80"></textarea>
                </div>

                <!-- 文本输入 -->
                <div id="textInput" class="form-group" style="display: none;">
                    <label>文本内容</label>
                    <textarea id="textContent" placeholder="请输入文本内容"></textarea>
                </div>

                <!-- 备注 -->
                <div class="form-group">
                    <label>备注 (可选)</label>
                    <textarea id="note" placeholder="请输入备注信息"></textarea>
                </div>

                <button type="submit" class="btn" id="submitBtn">提交作业</button>
            </form>
        </div>

        <!-- 加载状态 -->
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>正在提交，请稍候...</p>
        </div>

        <div class="back-link">
            <a href="/">← 返回首页</a>
        </div>
    </div>

    <script>
        // 全局变量
        let questions = [];
        let selectedQuestion = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadQuestions();
            
            // 绑定文件上传事件
            document.getElementById('imageFile').addEventListener('change', handleFileSelect);
            
            // 绑定表单提交事件
            document.getElementById('submitForm').addEventListener('submit', handleSubmit);
        });

        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // 加载可用的实验
        async function loadQuestions() {
            try {
                const response = await fetch('/public/questions');
                if (response.ok) {
                    questions = await response.json();
                    const select = document.getElementById('questionSelect');
                    select.innerHTML = '<option value="">请选择实验...</option>' + 
                        questions.map(q => `<option value="${q.id}">${q.title}</option>`).join('');
                } else {
                    showAlert('加载实验列表失败', 'error');
                }
            } catch (error) {
                showAlert('网络错误，请重试', 'error');
            }
        }

        // 加载实验信息
        function loadQuestionInfo() {
            const questionId = document.getElementById('questionSelect').value;
            if (!questionId) {
                document.getElementById('questionInfo').style.display = 'none';
                document.getElementById('submissionForm').style.display = 'none';
                return;
            }

            selectedQuestion = questions.find(q => q.id == questionId);
            if (!selectedQuestion) return;

            // 显示实验信息
            document.getElementById('questionTitle').textContent = selectedQuestion.title;
            document.getElementById('questionDescription').textContent = selectedQuestion.description || '暂无描述';
            
            // 显示提交类型信息
            const typeInfo = getSubmissionTypeInfo(selectedQuestion.submission_type);
            document.getElementById('submissionTypeInfo').textContent = typeInfo;
            
            // 显示相应的输入字段
            showSubmissionFields(selectedQuestion.submission_type);
            
            document.getElementById('questionInfo').style.display = 'block';
            document.getElementById('submissionForm').style.display = 'block';
        }

        // 获取提交类型信息
        function getSubmissionTypeInfo(type) {
            const types = {
                'image_data': '📷 需要上传图片和输入数据',
                'image_only': '📷 仅需要上传图片',
                'data_only': '📊 仅需要输入数据',
                'text': '📝 仅需要输入文本'
            };
            return types[type] || '请按要求提交';
        }

        // 显示相应的提交字段
        function showSubmissionFields(type) {
            // 隐藏所有字段
            document.getElementById('imageUpload').style.display = 'none';
            document.getElementById('dataInput').style.display = 'none';
            document.getElementById('textInput').style.display = 'none';
            
            // 根据类型显示字段
            switch(type) {
                case 'image_data':
                    document.getElementById('imageUpload').style.display = 'block';
                    document.getElementById('dataInput').style.display = 'block';
                    break;
                case 'image_only':
                    document.getElementById('imageUpload').style.display = 'block';
                    break;
                case 'data_only':
                    document.getElementById('dataInput').style.display = 'block';
                    break;
                case 'text':
                    document.getElementById('textInput').style.display = 'block';
                    break;
            }
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            const label = document.getElementById('imageLabel');
            
            if (file) {
                label.textContent = `✅ 已选择: ${file.name}`;
                label.classList.add('has-file');
            } else {
                label.textContent = '📷 点击选择图片文件 (支持 JPG, PNG)';
                label.classList.remove('has-file');
            }
        }

        // 处理表单提交
        async function handleSubmit(event) {
            event.preventDefault();
            
            if (!selectedQuestion) {
                showAlert('请先选择实验', 'error');
                return;
            }

            const studentNo = document.getElementById('studentNo').value.trim();
            if (!studentNo) {
                showAlert('请输入学号', 'error');
                return;
            }

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('submitBtn').disabled = true;

            try {
                const formData = new FormData();
                formData.append('question_id', selectedQuestion.id);
                formData.append('student_no', studentNo);
                formData.append('note', document.getElementById('note').value);

                // 根据提交类型添加相应数据
                const submissionType = selectedQuestion.submission_type;
                
                if (submissionType === 'image_data' || submissionType === 'image_only') {
                    const imageFile = document.getElementById('imageFile').files[0];
                    if (imageFile) {
                        formData.append('image', imageFile);
                    } else if (submissionType === 'image_only') {
                        throw new Error('请选择图片文件');
                    }
                }

                if (submissionType === 'image_data' || submissionType === 'data_only') {
                    const numericData = document.getElementById('numericData').value.trim();
                    if (numericData) {
                        // 解析数字数组
                        const numbers = numericData.split(',').map(n => parseFloat(n.trim())).filter(n => !isNaN(n));
                        formData.append('numeric', JSON.stringify(numbers));
                    }
                }

                if (submissionType === 'text') {
                    const textContent = document.getElementById('textContent').value.trim();
                    if (textContent) {
                        formData.append('text_content', textContent);
                    } else {
                        throw new Error('请输入文本内容');
                    }
                }

                const response = await fetch('/api/submissions', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    showAlert('提交成功！', 'success');
                    
                    // 重置表单
                    document.getElementById('submitForm').reset();
                    document.getElementById('imageLabel').textContent = '📷 点击选择图片文件 (支持 JPG, PNG)';
                    document.getElementById('imageLabel').classList.remove('has-file');
                } else {
                    const error = await response.json();
                    showAlert(error.detail || '提交失败', 'error');
                }
            } catch (error) {
                showAlert(error.message || '网络错误，请重试', 'error');
            } finally {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                document.getElementById('submitBtn').disabled = false;
            }
        }
    </script>
</body>
</html>
