<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算物理课堂展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-x: hidden;
        }

        .header {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .header .info {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(5px);
        }

        .controls button {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .controls button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .controls select {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 15px;
            margin: 0 10px;
            border-radius: 20px;
            font-size: 16px;
        }

        .display-area {
            padding: 30px;
            min-height: calc(100vh - 200px);
        }

        .grid-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .answer-card {
            background: rgba(255,255,255,0.95);
            color: #333;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .answer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.2);
        }

        .answer-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .answer-card img {
            width: 100%;
            max-height: 250px;
            object-fit: contain;
            border-radius: 10px;
            margin: 15px 0;
            border: 2px solid #ecf0f1;
        }

        .answer-info {
            margin: 10px 0;
            font-size: 14px;
            color: #7f8c8d;
        }

        .answer-info strong {
            color: #2c3e50;
        }

        .score {
            background: linear-gradient(45deg, #3498db, #2ecc71);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin-top: 10px;
        }

        .carousel-display {
            text-align: center;
            position: relative;
            max-width: 1000px;
            margin: 0 auto;
        }

        .carousel-item {
            background: rgba(255,255,255,0.95);
            color: #333;
            border-radius: 20px;
            padding: 40px;
            margin: 20px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
        }

        .carousel-item h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2em;
        }

        .carousel-item img {
            max-width: 80%;
            max-height: 400px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .carousel-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(52, 152, 219, 0.8);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            transition: all 0.3s ease;
        }

        .carousel-nav:hover {
            background: rgba(52, 152, 219, 1);
            transform: translateY(-50%) scale(1.1);
        }

        .carousel-nav.prev {
            left: 20px;
        }

        .carousel-nav.next {
            right: 20px;
        }

        .slide-counter {
            margin-top: 20px;
            font-size: 18px;
            color: rgba(255,255,255,0.8);
        }

        .comparison-display {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .comparison-item {
            background: rgba(255,255,255,0.95);
            color: #333;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            flex: 1;
            min-width: 250px;
            max-width: 300px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .comparison-item h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .comparison-item img {
            width: 100%;
            height: 180px;
            object-fit: contain;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid #ecf0f1;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.5em;
            color: rgba(255,255,255,0.8);
        }

        .no-data {
            text-align: center;
            padding: 50px;
            font-size: 1.3em;
            color: rgba(255,255,255,0.7);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .answer-card, .carousel-item, .comparison-item {
            animation: fadeIn 0.6s ease-out;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .grid-display {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .controls button, .controls select {
                margin: 5px;
                padding: 8px 15px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 计算物理课堂展示</h1>
        <div class="info">
            <span id="questionTitle">请选择题目</span> | 
            <span id="className">请选择班级</span> | 
            <span id="answerCount">0 个答案</span>
        </div>
    </div>

    <div class="controls">
        <select id="classFilter">
            <option value="">选择班级</option>
        </select>
        <select id="questionFilter">
            <option value="">选择题目</option>
        </select>
        <select id="displayMode">
            <option value="grid">网格模式</option>
            <option value="carousel">轮播模式</option>
            <option value="comparison">对比模式</option>
        </select>
        <button onclick="refreshDisplay()">🔄 刷新</button>
        <button onclick="toggleFullscreen()">🖥️ 全屏</button>
        <button onclick="startAutoRefresh()">⏰ 自动刷新</button>
    </div>

    <div class="display-area">
        <div id="displayContent" class="loading">
            <p>🔍 请选择班级和题目开始展示</p>
        </div>
    </div>

    <script>
        // 自动检测API基础URL，避免跨域问题
        const API_BASE = window.location.origin;
        let currentAnswers = [];
        let currentSlide = 0;
        let autoRefreshInterval = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadClasses();
            loadQuestions();
            
            // 从URL参数获取初始值
            const urlParams = new URLSearchParams(window.location.search);
            const classId = urlParams.get('class_id');
            const questionId = urlParams.get('question_id');
            
            if (classId) {
                document.getElementById('classFilter').value = classId;
            }
            if (questionId) {
                document.getElementById('questionFilter').value = questionId;
            }
            
            if (classId && questionId) {
                updateDisplay();
            }
        });

        // 加载班级列表
        async function loadClasses() {
            try {
                const response = await fetch(`${API_BASE}/public/classes`);
                const classes = await response.json();

                const select = document.getElementById('classFilter');
                select.innerHTML = '<option value="">选择班级</option>' +
                    classes.filter(cls => cls.is_active).map(cls => `<option value="${cls.id}">${cls.name}</option>`).join('');
            } catch (error) {
                console.error('加载班级失败:', error);
            }
        }

        // 加载题目列表
        async function loadQuestions() {
            try {
                const response = await fetch(`${API_BASE}/public/questions`);
                const questions = await response.json();

                const select = document.getElementById('questionFilter');
                select.innerHTML = '<option value="">选择题目</option>' +
                    questions.filter(q => q.is_open).map(q => `<option value="${q.id}">${q.title}</option>`).join('');
            } catch (error) {
                console.error('加载题目失败:', error);
            }
        }

        // 更新显示内容
        async function updateDisplay() {
            const classId = document.getElementById('classFilter').value;
            const questionId = document.getElementById('questionFilter').value;
            const mode = document.getElementById('displayMode').value;
            
            if (!classId || !questionId) {
                document.getElementById('displayContent').innerHTML = 
                    '<div class="no-data"><p>🔍 请选择班级和题目开始展示</p></div>';
                return;
            }
            
            document.getElementById('displayContent').innerHTML = 
                '<div class="loading"><p>📡 正在加载答案...</p></div>';
            
            try {
                const response = await fetch(`${API_BASE}/public/submissions?class_id=${classId}&question_id=${questionId}&visible=true`);
                const answers = await response.json();
                
                currentAnswers = answers;
                currentSlide = 0;
                
                // 更新头部信息
                updateHeaderInfo(classId, questionId, answers.length);
                
                if (answers.length === 0) {
                    document.getElementById('displayContent').innerHTML = 
                        '<div class="no-data"><p>📝 该题目暂无可见的答案</p></div>';
                    return;
                }
                
                // 根据模式显示内容
                switch(mode) {
                    case 'grid':
                        showGridDisplay(answers);
                        break;
                    case 'carousel':
                        showCarouselDisplay(answers);
                        break;
                    case 'comparison':
                        showComparisonDisplay(answers);
                        break;
                }
                
            } catch (error) {
                console.error('加载答案失败:', error);
                document.getElementById('displayContent').innerHTML = 
                    '<div class="no-data"><p>❌ 加载失败，请检查网络连接</p></div>';
            }
        }

        // 更新头部信息
        async function updateHeaderInfo(classId, questionId, answerCount) {
            try {
                const [classRes, questionRes] = await Promise.all([
                    fetch(`${API_BASE}/admin/classes`),
                    fetch(`${API_BASE}/admin/questions`)
                ]);
                
                const classes = await classRes.json();
                const questions = await questionRes.json();
                
                const selectedClass = classes.find(c => c.id == classId);
                const selectedQuestion = questions.find(q => q.id == questionId);
                
                document.getElementById('className').textContent = selectedClass ? selectedClass.name : '未知班级';
                document.getElementById('questionTitle').textContent = selectedQuestion ? selectedQuestion.title : '未知题目';
                document.getElementById('answerCount').textContent = `${answerCount} 个答案`;
                
            } catch (error) {
                console.error('更新头部信息失败:', error);
            }
        }

        // 网格显示模式
        function showGridDisplay(answers) {
            const html = `
                <div class="grid-display">
                    ${answers.map(answer => `
                        <div class="answer-card">
                            <h3>👤 ${answer.student_name || answer.student_no}</h3>
                            <div class="answer-info"><strong>学号:</strong> ${answer.student_no}</div>
                            <div class="answer-info"><strong>提交时间:</strong> ${new Date(answer.created_at).toLocaleString()}</div>
                            ${answer.image_url ? `
                                <img src="${answer.image_url}" alt="答案图片" onclick="showImageModal('${answer.image_url}')">
                            ` : ''}
                            ${answer.numeric && answer.numeric.length > 0 ? `
                                <div class="answer-info"><strong>数据:</strong> ${answer.numeric.slice(0, 5).map(n => typeof n === 'number' ? n.toFixed(2) : n).join(', ')}${answer.numeric.length > 5 ? '...' : ''}</div>
                            ` : ''}
                            ${answer.note ? `<div class="answer-info"><strong>备注:</strong> ${answer.note}</div>` : ''}
                            ${answer.score ? `<div class="score">分数: ${answer.score}</div>` : ''}
                        </div>
                    `).join('')}
                </div>
            `;
            document.getElementById('displayContent').innerHTML = html;
        }

        // 轮播显示模式
        function showCarouselDisplay(answers) {
            const html = `
                <div class="carousel-display">
                    ${answers.map((answer, index) => `
                        <div class="carousel-item" style="display: ${index === 0 ? 'block' : 'none'};" data-slide="${index}">
                            <h2>👤 ${answer.student_name || answer.student_no}</h2>
                            <div style="font-size: 1.2em; color: #7f8c8d; margin-bottom: 20px;">学号: ${answer.student_no}</div>
                            ${answer.image_url ? `
                                <img src="${answer.image_url}" alt="答案图片" onclick="showImageModal('${answer.image_url}')">
                            ` : ''}
                            ${answer.note ? `<div style="margin: 20px 0; font-size: 1.1em; color: #2c3e50;">${answer.note}</div>` : ''}
                            <div style="color: #95a5a6; margin-top: 20px;">提交时间: ${new Date(answer.created_at).toLocaleString()}</div>
                            ${answer.score ? `<div class="score" style="margin-top: 15px; font-size: 1.2em;">分数: ${answer.score}</div>` : ''}
                        </div>
                    `).join('')}
                    <button class="carousel-nav prev" onclick="prevSlide()">‹</button>
                    <button class="carousel-nav next" onclick="nextSlide()">›</button>
                    <div class="slide-counter">
                        <span id="slideInfo">1 / ${answers.length}</span>
                    </div>
                </div>
            `;
            document.getElementById('displayContent').innerHTML = html;
        }

        // 对比显示模式
        function showComparisonDisplay(answers) {
            const html = `
                <div class="comparison-display">
                    ${answers.map(answer => `
                        <div class="comparison-item">
                            <h4>👤 ${answer.student_name || answer.student_no}</h4>
                            <div style="font-size: 12px; color: #7f8c8d; margin-bottom: 10px;">${answer.student_no}</div>
                            ${answer.image_url ? `
                                <img src="${answer.image_url}" alt="答案图片" onclick="showImageModal('${answer.image_url}')">
                            ` : ''}
                            <div style="font-size: 12px; color: #95a5a6; margin-top: 10px;">
                                ${new Date(answer.created_at).toLocaleString()}
                            </div>
                            ${answer.score ? `<div class="score" style="margin-top: 10px; font-size: 14px;">分数: ${answer.score}</div>` : ''}
                        </div>
                    `).join('')}
                </div>
            `;
            document.getElementById('displayContent').innerHTML = html;
        }

        // 轮播控制
        function nextSlide() {
            if (currentAnswers.length === 0) return;
            currentSlide = (currentSlide + 1) % currentAnswers.length;
            showSlide(currentSlide);
        }

        function prevSlide() {
            if (currentAnswers.length === 0) return;
            currentSlide = (currentSlide - 1 + currentAnswers.length) % currentAnswers.length;
            showSlide(currentSlide);
        }

        function showSlide(n) {
            const items = document.querySelectorAll('.carousel-item');
            items.forEach((item, index) => {
                item.style.display = index === n ? 'block' : 'none';
            });
            const slideInfo = document.getElementById('slideInfo');
            if (slideInfo) {
                slideInfo.textContent = `${n + 1} / ${currentAnswers.length}`;
            }
        }

        // 刷新显示
        function refreshDisplay() {
            updateDisplay();
        }

        // 全屏切换
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 自动刷新
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                event.target.textContent = '⏰ 自动刷新';
            } else {
                autoRefreshInterval = setInterval(updateDisplay, 10000); // 每10秒刷新
                event.target.textContent = '⏹️ 停止刷新';
            }
        }

        // 图片模态框
        function showImageModal(imageUrl) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.9); display: flex; justify-content: center;
                align-items: center; z-index: 1000; cursor: pointer;
            `;
            modal.innerHTML = `<img src="${imageUrl}" style="max-width: 90%; max-height: 90%; border-radius: 10px;">`;
            modal.onclick = () => document.body.removeChild(modal);
            document.body.appendChild(modal);
        }

        // 事件监听
        document.getElementById('classFilter').addEventListener('change', updateDisplay);
        document.getElementById('questionFilter').addEventListener('change', updateDisplay);
        document.getElementById('displayMode').addEventListener('change', updateDisplay);

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (document.getElementById('displayMode').value === 'carousel') {
                if (e.key === 'ArrowLeft') prevSlide();
                if (e.key === 'ArrowRight') nextSlide();
            }
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                refreshDisplay();
            }
        });
    </script>
</body>
</html>
