#!/usr/bin/env python3
"""
测试课堂展示功能修复
"""

import requests
import json

BASE_URL = "http://localhost:30200"

def test_admin_login():
    """测试管理员登录"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/admin/login", json=login_data)
    if response.status_code == 200:
        token_data = response.json()
        return token_data["access_token"]
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def set_submission_visible(token, submission_id):
    """设置提交为可见"""
    headers = {"Authorization": f"Bearer {token}"}
    update_data = {"visible": True}
    
    response = requests.patch(f"{BASE_URL}/admin/submissions/{submission_id}", 
                            json=update_data, headers=headers)
    
    if response.status_code == 200:
        print(f"✅ 提交 {submission_id} 已设置为可见")
        return True
    else:
        print(f"❌ 设置提交可见失败: {response.status_code} - {response.text}")
        return False

def test_public_submissions():
    """测试公开提交API"""
    print("\n🔍 测试公开提交API...")
    
    # 测试所有提交
    response = requests.get(f"{BASE_URL}/public/submissions?class_id=1&question_id=1")
    if response.status_code == 200:
        all_submissions = response.json()
        print(f"   所有提交数量: {len(all_submissions)}")
    else:
        print(f"   获取所有提交失败: {response.status_code}")
        return
    
    # 测试可见提交
    response = requests.get(f"{BASE_URL}/public/submissions?class_id=1&question_id=1&visible=true")
    if response.status_code == 200:
        visible_submissions = response.json()
        print(f"   可见提交数量: {len(visible_submissions)}")
        
        if visible_submissions:
            print("   ✅ 可见提交示例:")
            for sub in visible_submissions[:1]:  # 只显示第一个
                print(f"      学号: {sub['student_no']}")
                print(f"      姓名: {sub['student_name']}")
                print(f"      图片: {'有' if sub['image_url'] else '无'}")
                print(f"      数据: {'有' if sub['numeric'] else '无'}")
        else:
            print("   ⚠️  没有可见的提交")
    else:
        print(f"   获取可见提交失败: {response.status_code}")

def main():
    print("🧪 开始测试课堂展示功能修复...")
    
    # 1. 登录获取token
    print("\n1️⃣ 管理员登录...")
    token = test_admin_login()
    if not token:
        return
    print("   ✅ 登录成功")
    
    # 2. 获取提交列表
    print("\n2️⃣ 获取提交列表...")
    response = requests.get(f"{BASE_URL}/public/submissions?class_id=1&question_id=1")
    if response.status_code != 200:
        print(f"   ❌ 获取提交失败: {response.status_code}")
        return
    
    submissions = response.json()
    print(f"   找到 {len(submissions)} 个提交")
    
    if not submissions:
        print("   ⚠️  没有找到提交，请先运行示例程序提交答案")
        return
    
    # 3. 设置第一个提交为可见
    print("\n3️⃣ 设置提交为可见...")
    first_submission = submissions[0]
    success = set_submission_visible(token, first_submission["id"])
    
    if success:
        # 4. 测试公开API
        test_public_submissions()
        
        # 5. 提供测试链接
        print(f"\n🎉 修复测试完成！")
        print(f"📱 管理页面: {BASE_URL}/static/admin.html")
        print(f"🎭 课堂展示: {BASE_URL}/static/classroom_display.html?class_id=1&question_id=1")
        print(f"📊 学生视图: {BASE_URL}/public/students/view?class_id=1")

if __name__ == "__main__":
    main()
