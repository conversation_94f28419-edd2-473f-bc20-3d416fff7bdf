#!/usr/bin/env python3
"""
测试CORS修复效果
"""

import requests
import time

def test_cors_fix():
    """测试CORS修复效果"""
    print("🧪 测试CORS修复效果...")
    
    base_url = "http://localhost:30200"
    
    # 测试各个API端点
    endpoints = [
        "/public/classes",
        "/public/questions", 
        "/public/submissions?class_id=1&question_id=1",
        "/public/submissions?class_id=1&question_id=1&visible=true"
    ]
    
    print(f"\n🌐 测试服务器: {base_url}")
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\n📡 测试: {endpoint}")
        
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    print(f"   ✅ 成功 - 返回 {len(data)} 条记录")
                else:
                    print(f"   ✅ 成功 - 返回数据类型: {type(data)}")
                    
                # 检查CORS头
                cors_headers = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                }
                
                if cors_headers['Access-Control-Allow-Origin']:
                    print(f"   🔓 CORS配置正确: {cors_headers['Access-Control-Allow-Origin']}")
                else:
                    print(f"   ⚠️  未检测到CORS头")
                    
            else:
                print(f"   ❌ 失败 - 状态码: {response.status_code}")
                print(f"      错误信息: {response.text[:100]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 请求失败: {e}")
    
    # 测试课堂展示页面
    print(f"\n🎭 测试课堂展示页面...")
    display_url = f"{base_url}/static/classroom_display.html?class_id=1&question_id=1"
    
    try:
        response = requests.get(display_url, timeout=10)
        if response.status_code == 200:
            print(f"   ✅ 课堂展示页面加载成功")
            
            # 检查页面内容是否包含修复后的API_BASE配置
            content = response.text
            if "window.location.origin" in content:
                print(f"   ✅ API_BASE配置已修复为动态获取")
            else:
                print(f"   ⚠️  API_BASE配置可能未修复")
        else:
            print(f"   ❌ 页面加载失败 - 状态码: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ 页面请求失败: {e}")
    
    print(f"\n🎉 测试完成！")
    print(f"📱 管理页面: {base_url}/static/admin.html")
    print(f"🎭 课堂展示: {base_url}/static/classroom_display.html?class_id=1&question_id=1")
    print(f"📊 学生视图: {base_url}/public/students/view?class_id=1")

if __name__ == "__main__":
    test_cors_fix()
