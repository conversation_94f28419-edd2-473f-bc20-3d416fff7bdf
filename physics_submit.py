#!/usr/bin/env python3
"""
计算物理课程答案提交工具
简化版本，供学生在Python程序中直接调用
"""

import requests
import json
import matplotlib.pyplot as plt
import numpy as np
from io import BytesIO
import base64
import os
from datetime import datetime

# 服务器配置
SERVER_URL = "http://43.155.146.157:30200"

def submit_answer(student_no, question_id, image_data=None, numeric_data=None, text_data=None, note=""):
    """
    提交答案到计算物理系统
    
    参数:
        student_no (str): 学号
        question_id (int): 题目ID
        image_data: 图片数据，可以是：
            - matplotlib图形对象 (plt.figure())
            - 图片文件路径 (str)
            - 图片二进制数据 (bytes)
        numeric_data (list): 数值数据列表
        text_data (str): 文本数据
        note (str): 备注信息
    
    返回:
        dict: 提交结果
    """
    
    try:
        # 准备提交数据
        files = {}
        data = {
            'student_no': student_no,
            'question_id': question_id,
            'note': note or f"计算物理答案提交 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        }
        
        # 处理图片数据
        if image_data is not None:
            image_bytes = _process_image(image_data)
            if image_bytes:
                files['image'] = ('result.png', image_bytes, 'image/png')
        
        # 处理数值数据
        if numeric_data is not None:
            if isinstance(numeric_data, (list, tuple, np.ndarray)):
                # 转换numpy数组为列表
                if isinstance(numeric_data, np.ndarray):
                    numeric_data = numeric_data.tolist()
                data['numeric'] = json.dumps(numeric_data)
            else:
                data['numeric'] = json.dumps([numeric_data])
        
        # 处理文本数据
        if text_data is not None:
            data['text'] = str(text_data)
        
        # 发送请求
        response = requests.post(f"{SERVER_URL}/api/submissions", 
                               files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 答案提交成功!")
            print(f"   提交ID: {result.get('id', 'N/A')}")
            print(f"   学号: {student_no}")
            print(f"   题目ID: {question_id}")
            return {"success": True, "data": result}
        else:
            error_msg = f"提交失败: HTTP {response.status_code}"
            if response.text:
                error_msg += f" - {response.text[:200]}"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}
            
    except requests.exceptions.RequestException as e:
        error_msg = f"网络请求失败: {e}"
        print(f"❌ {error_msg}")
        return {"success": False, "error": error_msg}
    except Exception as e:
        error_msg = f"提交过程出错: {e}"
        print(f"❌ {error_msg}")
        return {"success": False, "error": error_msg}

def _process_image(image_data):
    """处理图片数据"""
    try:
        # 如果是matplotlib图形
        if hasattr(image_data, 'savefig'):
            buffer = BytesIO()
            image_data.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            return buffer.getvalue()
        
        # 如果是当前matplotlib图形
        elif image_data == 'current' or image_data is True:
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            return buffer.getvalue()
        
        # 如果是文件路径
        elif isinstance(image_data, str) and os.path.exists(image_data):
            with open(image_data, 'rb') as f:
                return f.read()
        
        # 如果是二进制数据
        elif isinstance(image_data, bytes):
            return image_data
        
        else:
            print("⚠️  无法识别的图片数据格式")
            return None
            
    except Exception as e:
        print(f"⚠️  处理图片数据时出错: {e}")
        return None

def quick_submit(student_no, question_id, note=""):
    """
    快速提交当前matplotlib图形
    
    参数:
        student_no (str): 学号
        question_id (int): 题目ID
        note (str): 备注信息
    """
    return submit_answer(student_no, question_id, image_data='current', note=note)

def submit_plot_and_data(student_no, question_id, x_data, y_data, title="计算结果", note=""):
    """
    提交绘图和数据
    
    参数:
        student_no (str): 学号
        question_id (int): 题目ID
        x_data (list): x轴数据
        y_data (list): y轴数据
        title (str): 图形标题
        note (str): 备注信息
    """
    
    # 创建图形
    plt.figure(figsize=(10, 6))
    plt.plot(x_data, y_data, 'b-', linewidth=2)
    plt.title(f"{title} - 学号: {student_no}")
    plt.xlabel('x')
    plt.ylabel('y')
    plt.grid(True, alpha=0.3)
    
    # 提交图形和数据
    result = submit_answer(student_no, question_id, 
                          image_data='current', 
                          numeric_data=y_data, 
                          note=note)
    
    plt.close()  # 关闭图形以释放内存
    return result

def submit_calculation_result(student_no, question_id, result_value, description="", note=""):
    """
    提交计算结果
    
    参数:
        student_no (str): 学号
        question_id (int): 题目ID
        result_value: 计算结果（数值或列表）
        description (str): 结果描述
        note (str): 备注信息
    """
    
    full_note = f"{description}\n结果: {result_value}\n{note}" if description else note
    
    return submit_answer(student_no, question_id, 
                        numeric_data=result_value if isinstance(result_value, (list, tuple)) else [result_value],
                        text_data=description,
                        note=full_note)

# 使用示例
if __name__ == "__main__":
    print("📚 计算物理答案提交工具")
    print("=" * 40)
    
    # 示例1: 提交简单计算结果
    print("\n示例1: 提交计算结果")
    print("submit_calculation_result('2022062030', 1, 3.14159, '圆周率计算')")
    
    # 示例2: 提交绘图和数据
    print("\n示例2: 提交绘图和数据")
    print("x = np.linspace(0, 2*np.pi, 100)")
    print("y = np.sin(x)")
    print("submit_plot_and_data('2022062030', 1, x, y, '正弦函数')")
    
    # 示例3: 快速提交当前图形
    print("\n示例3: 快速提交当前图形")
    print("plt.plot([1,2,3], [1,4,9])")
    print("quick_submit('2022062030', 1, '二次函数图像')")
    
    print(f"\n🌐 服务器地址: {SERVER_URL}")
    print("💡 确保服务器运行正常后再进行提交")
