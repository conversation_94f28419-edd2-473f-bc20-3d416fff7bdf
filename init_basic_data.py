#!/usr/bin/env python3
"""
初始化基本数据 - 创建示例班级、学生和题目
"""

import requests
import json

# 服务器配置
SERVER_URL = "http://localhost:30200"

def get_admin_token():
    """获取管理员token"""
    response = requests.post(f"{SERVER_URL}/admin/login", 
                           json={"username": "admin", "password": "admin123"})
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        raise Exception(f"登录失败: {response.text}")

def create_class(token, name, code):
    """创建班级"""
    headers = {"Authorization": f"Bearer {token}"}
    data = {"name": name, "code": code}
    
    response = requests.post(f"{SERVER_URL}/admin/classes", 
                           json=data, headers=headers)
    if response.status_code == 200:
        class_data = response.json()
        print(f"✅ 创建班级成功: {name} (ID: {class_data['id']})")
        return class_data
    else:
        print(f"❌ 创建班级失败: {response.text}")
        return None

def create_student(token, student_no, name, class_id):
    """创建学生"""
    headers = {"Authorization": f"Bearer {token}"}
    data = {"student_no": student_no, "name": name, "class_id": class_id}
    
    response = requests.post(f"{SERVER_URL}/admin/students", 
                           json=data, headers=headers)
    if response.status_code == 200:
        student_data = response.json()
        print(f"✅ 创建学生成功: {name} ({student_no})")
        return student_data
    else:
        print(f"❌ 创建学生失败: {response.text}")
        return None

def create_question(token, title, description, class_id, question_type="submit_result"):
    """创建题目"""
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "title": title,
        "description": description,
        "class_id": class_id,
        "question_type": question_type,
        "submission_type": "image_data",
        "is_open": True
    }
    
    response = requests.post(f"{SERVER_URL}/admin/questions",
                           json=data, headers=headers)
    if response.status_code == 200:
        question_data = response.json()
        question_id = question_data.get('question_id', question_data.get('id', 'Unknown'))
        print(f"✅ 创建题目成功: {title} (ID: {question_id})")
        return question_data
    else:
        print(f"❌ 创建题目失败: {response.text}")
        return None

def activate_class(token, class_id):
    """激活班级"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.patch(f"{SERVER_URL}/admin/classes/{class_id}/activate", 
                            headers=headers)
    if response.status_code == 200:
        print(f"✅ 激活班级成功: ID {class_id}")
        return True
    else:
        print(f"❌ 激活班级失败: {response.text}")
        return False

def main():
    """主函数"""
    print("🚀 初始化基本数据")
    print("=" * 50)
    
    try:
        # 获取管理员token
        print("🔐 获取管理员权限...")
        token = get_admin_token()
        print("✅ 管理员登录成功")
        
        # 创建班级
        print("\n📚 创建示例班级...")
        class1 = create_class(token, "计算物理实验班A", "PHYA")
        class2 = create_class(token, "计算物理实验班B", "PHYB")
        
        if not class1 or not class2:
            print("❌ 班级创建失败，退出")
            return
        
        # 激活第一个班级
        print(f"\n⚡ 激活班级...")
        activate_class(token, class1['id'])
        
        # 创建学生
        print(f"\n👥 创建示例学生...")
        students_data = [
            ("2022062030", "吴可", class1['id']),
            ("2022062031", "张三", class1['id']),
            ("2022062032", "李四", class1['id']),
            ("2022062033", "王五", class1['id']),
            ("2022062034", "赵六", class2['id']),
            ("2022062035", "钱七", class2['id']),
        ]
        
        for student_no, name, class_id in students_data:
            create_student(token, student_no, name, class_id)
        
        # 创建题目
        print(f"\n📝 创建示例题目...")
        questions_data = [
            ("简谐振动实验", "使用Python计算并绘制简谐振动的位移-时间图像，分析振动特性", class1['id']),
            ("波的干涉实验", "计算并可视化两个波源的干涉图样，分析干涉特征", class1['id']),
            ("单摆运动模拟", "数值求解单摆运动方程并分析周期与振幅的关系", class1['id']),
            ("蒙特卡罗计算π", "使用随机投点法估算圆周率，分析收敛性和误差", class2['id']),
        ]
        
        for title, description, class_id in questions_data:
            create_question(token, title, description, class_id)
        
        print(f"\n🎉 基本数据初始化完成！")
        print("=" * 50)
        print("📊 创建的数据:")
        print("   - 2个班级: 计算物理实验班A, 计算物理实验班B")
        print("   - 6个学生: 吴可, 张三, 李四, 王五, 赵六, 钱七")
        print("   - 4个题目: 简谐振动, 波的干涉, 单摆运动, 蒙特卡罗π")
        print("   - 激活班级: 计算物理实验班A")
        print()
        print("🌐 现在可以访问管理端查看数据:")
        print("   http://localhost:30200/static/admin.html")
        print("   用户名: admin")
        print("   密码: admin123")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")

if __name__ == "__main__":
    main()
